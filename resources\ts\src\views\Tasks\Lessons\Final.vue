<template>
    <div class="full-view-banner banner" v-bind:style="{ 'backgroundImage': 'url(' + lesson.background_imagepath + ')' }">
        <div v-if="lesson.background_video" class="banner-video" v-html="lesson.background_video"></div>
        <div style="position:absolute;width:100%;height:100%;opacity:.3;background:#000;"></div>
        <div class="banner_detail_box w-450px">
            <div v-if="lesson.badges?.length && (!lesson.feedback && lesson.compeletedpercent !== 100)" class="mt-4 mb-4">
                <div class="row g-3">
                    <div v-for="badge in lesson.badges" :key="badge.id" class="col-6">
                        <div class="d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3">
                            <img :src="badge?.image_fullpath" :alt="badge.name" class="me-3" width="60" height="60">
                            <div>
                                <p class="mb-1 fw-bold text-dark">{{ badge.name }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <h1 class="fw-normal text-light">Final Step</h1>
            <h1 class="display-4 fw-normal text-light"><span v-if="lesson.response">Upload & </span>Submit</h1>
            <div class="row mt-5" v-if="lesson.response">
                <div class="col-12 fs-6 text-light d-flex response-upload-input">
                    <input type="file" id="taskfiles" class="form-control rounded-0" @change="onFileChanged($event)" />
                    <label for="taskfiles">{{ fileName ?? 'Upload your task' }}</label>
                </div>
                <p v-if="responseError.length" v-text="responseError" class="form-error mt-2 ms-2"></p>
            </div>
            <div class="row mt-5">
                <div class="col-sm-12">
                    <button :disabled="!currentUser.isStudent" v-on:click="saveResponse()" class="btn btn-white-custom btn-lg rounded-0 w-100 p-md-5"> Submit Task </button>
                </div>
            </div>
            <!-- <div class="modal fade" id="kt_modal_trailer" tabindex="-1" style="display: none" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered mw-650px">
                    <div class="modal-content">
                        <div class="modal-body" v-html="lesson.background_video"></div>
                    </div>
                </div>
            </div> -->
        </div>
    </div>
    <div v-bind:class="{ 'row': lesson.steps.length < 6 }" class="d-flex module-sections">
        <template v-for="(step, index) in lesson.steps" :key="step.id">
            <div v-bind:class="[(lesson.steps.length < 6) ? 'col' : 'col-6 col-sm-4 col-md-2', step.user_response ? 'bg-black' : '']" @click="gotosection(step.id, step.user_response, index)" class=" text-center  p-0">
                <div class="module-section d-flex flex-column justify-content-center align-items-center py-5" v-bind:class="{ 'bg-white': !step.user_response }">
                    <span class="svg-icon svg-icon-primary svg-icon-2x">
                        <svg v-if="step.user_response" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <mask fill="white">
                                    <use xlink:href="#path-1" />
                                </mask>
                                <g />
                                <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" />
                            </g>
                        </svg>
                        <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <mask fill="white">
                                    <use xlink:href="#path-1" />
                                </mask>
                                <g />
                                <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" />
                            </g>
                        </svg>
                    </span>
                    <p class=" m-0 px-5" :class="{ 'text-white': step.user_response }" v-html="step.title"></p>
                    <p class=" m-0" :class="{ 'text-white': step.user_response }">
                        <span v-if="step.estimated_time && step.estimated_time.hours" v-text="step.estimated_time.hours + 'h '"></span>
                        <span v-if="step.estimated_time && step.estimated_time.minutes" v-text="step.estimated_time.minutes + 'm'"></span>
                    </p>
                </div>
            </div>
        </template>
        <div class="text-center p-0" :class="[(lesson.steps.length < 6) ? 'col' : 'col-6 col-sm-4 col-md-2', lesson.user_response ? 'bg-black' : '']">
            <div class="module-section d-flex flex-column justify-content-center align-items-center py-5">
                <span class="svg-icon svg-icon-primary svg-icon-2x">
                    <svg v-if="lesson.user_response" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <mask fill="white">
                                <use xlink:href="#path-1" />
                            </mask>
                            <g />
                            <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" />
                        </g>
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <mask fill="white">
                                <use xlink:href="#path-1" />
                            </mask>
                            <g />
                            <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" />
                        </g>
                    </svg>
                </span>
                <p class="m-0" :class="{ 'text-white': lesson.user_response }">Submit</p>
                <!-- <p class="m-0" :class="{ 'text-white': lesson.user_response }">&nbsp;</p> -->
            </div>
        </div>
        <div v-if="lesson.badges?.length && (lesson.feedback && lesson.compeletedpercent == 100)" class="mt-4 mb-4">
                <div class="row g-3">
                    <div v-for="badge in lesson.badges" :key="badge.id" class="col-6">
                        <div class="d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3">
                            <img :src="badge?.image_fullpath" :alt="badge.name" class="me-3" width="60" height="60">
                            <div>
                                <p class="mb-1 fw-bold text-dark">{{ badge.name }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</template>

<script lang="ts">
    import {
        defineComponent,
        ref,
        onMounted,
        nextTick
    } from "vue";
    import ApiService from "@/core/services/ApiService";
    import {
        useStore
    } from "vuex";
    import {
        useRouter,
        useRoute
    } from "vue-router";
    export default defineComponent({
        name: "lessons-final-step",
        components: {},
        setup(props) {
            const store = useStore();
            const route = useRoute();
            const router = useRouter();
            const currentUser = store.getters.currentUser;
            onMounted(() => {
                fetchLessonDetail();
            });
            const file = ref<File | null>();
            const lesson = ref();
            const latestStep = ref();
            const currentlesson = ref();
            const favLesson = ref();
            const fileName = ref();
            const showRelatedLessonsList = ref();
            lesson.value = {
                id: 1,
                background_imagepath: null,
                background_video: null,
                response: false,
                steps: [],
            };
            latestStep.value = 0;
            currentlesson.value = route.params.id;
            const form = ref<HTMLFormElement>();

            function onFileChanged($event: Event) {
                const target = $event.target as HTMLInputElement;
                if (target && target.files) {
                    file.value = target.files[0];

                    fileName.value = target.files[0].name;
                }
            }
            const lessonresponseform = ref();
            const responseError = ref("");
            lessonresponseform.value = {
                response: "",
                nofile: !lesson.value.response,
            };
            async function saveResponse() {
                if (lesson.value.response && file.value) {
                    try {
                        // save file.value
                        lessonresponseform.value.response = file.value;
                        lessonresponseform.value.nofile = !lesson.value.response;
                        ApiService.upload(`api/lessons/` + props.id + `/submit-task`, lessonresponseform.value).then(({
                            data
                        }) => {
                            if (typeof data.error != "undefined") {
                                responseError.value = data.error;
                            } else {
                                localStorage.setItem('showScormResult', "1");
                                router.push({
                                    name: 'task-lessons-view-response',
                                    params: {
                                        id: lesson.value.id
                                    },
                                }).then(() => { });
                            }
                        }).catch(({
                            response
                        }) => { });
                    } catch (error) {
                        console.error(error);
                        form.value?.reset();
                        file.value = null;
                    } finally { }
                } else if (lesson.value.response && !file.value) {
                    responseError.value = "Please upload your task before submitting.";
                } else if (!lesson.value.response) {
                    ApiService.upload(`api/lessons/` + props.id + `/submit-task`, lessonresponseform.value).then(({
                        data
                    }) => {
                        if (typeof data.error != "undefined") {
                            responseError.value = data.error;
                        } else {
                            localStorage.setItem('showScormResult', "1");
                            router.push({
                                name: 'task-lessons-view-response',
                                params: {
                                    id: lesson.value.id
                                },
                            }).then(() => { });
                        }
                    }).catch(({
                        response
                    }) => { });
                }
            }
            const fetchLessonDetail = async () => {
                try {
                 const { data } = await ApiService.get(`api/lessons`, currentlesson.value);
                    lesson.value = data;

                    await nextTick(); // Wait for DOM update

                    const moduleSections = document.querySelector('.module-sections');
                    if (moduleSections) {
                        // select the last child (final step)
                            const sectionDiv = moduleSections.lastElementChild;
                            if (sectionDiv && sectionDiv.scrollIntoView) {
                                sectionDiv.scrollIntoView({
                                    behavior: 'smooth',
                                    inline: 'center',
                                    block: 'nearest'
                                });

                        }
                    }

                } catch (error: any) {
                    if (error.response) {
                        console.log(error.response.data);
                        console.log(error.response.status);
                        console.log(error.response.headers);
                    } else if (error.request) {
                        console.log(error.request);
                    } else {
                        console.log("Error", error.message);
                    }
                    console.log(error.config);
                }
            };
            const toggleRelated = () => {
                showRelatedLessonsList.value = !showRelatedLessonsList.value;
            };
            const favouriteLesson = (id) => {
                favLesson.value = {
                    id: id
                };
                ApiService.post(`api/lessons/` + id + `/fav`, favLesson.value).then(({
                    data
                }) => {
                    lesson.value.favourite = data.favourite;
                }).catch(({
                    response
                }) => { });
            };
            const gotosection = (sectionid, cannavigate, index) => {
                if (cannavigate) {
                    router.push({
                        name: "task-lessons-section-detail",
                        params: {
                            id: lesson.value.id,
                            sectionid: (index + 1)
                        },
                    })
                }
            };
            return {
                currentUser,
                lesson,
                toggleRelated,
                currentlesson,
                showRelatedLessonsList,
                latestStep,
                favouriteLesson,
                saveResponse,
                onFileChanged,
                gotosection,
                responseError,
                fileName,
            };
        },
        props: ["id"],
    });
</script>

<style>
    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #000000 !important;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #000000 !important;
    }

    .wrap {
        overflow: hidden;
        max-width: 55ch;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .banner_detail_box {
        position: absolute;
        top: 50%;
        left: 20%;
        transform: translate(-50%, -50%);
    }

    .response-upload-input>input {
        /* width: 110px;   // for multiple*/
        width: 104px;
    }

    .response-upload-input>label {
        flex-grow: 1;
        color: #5E6278;
        background-color: #fff;
        border: 1px solid #E4E6EF;
        border-left: none;
        padding: 0.775rem 1rem;
        font-size: 1.1rem;
        font-weight: 500;
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .response-upload-input>input:focus+label {
        border-color: #B5B5C3;
    }

    .btn-white-custom {
        background: #fff;
        color: #000;
    }

    .btn-white-custom:hover,
    .btn.btn-white-custom:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }

    .btn-white-custom:disabled {
        background-color: #fff;
        opacity: 1;
    }

    .pointer {
        cursor: pointer;
    }

    .overlay {
        overflow: overlay;
    }

    .related {
        right: 5% !important;
    }

    div#kt_app_content {
        padding-top: 0px;
        padding-bottom: 0px;
    }

    .btn-white {
        border: 1px solid #000 !important;
    }

    .btn-white:hover,
    .btn.btn-white:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #000000 !important;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #000000 !important;
    }

    .module-sections {
        overflow: auto hidden;
        margin-left: -30px;
        margin-right: -30px;
    }

    .app-content {
        padding: 0px;
    }

    .full-page {
        margin-left: -20px;
        margin-right: -20px;
    }

    .banner {
        /* background-image: url("/images/vwe/home-parallax.jpg"); */
        background-color: #000;
        min-height: calc(45.25vw - 149px);
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
    }

    .full-view-banner {
        margin-left: -30px;
        margin-right: -30px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        /* height: 100%; */
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .page-content {
        top: 40%;
        /* text-align: center; */
        position: absolute;
        width: 100%;
        padding: 0px 15px;
    }

    .module-section {
        border-top: 1px solid;
        border-bottom: 1px solid;
        border-left: 1px solid;
        cursor: pointer;
        height: 100px;
    }

    .module-sections>.text-center:last-of-type>.module-section {
        border-right: 1px solid;
    }

    @media (max-width: 1280px) {
        .banner {
            height: 56.25vw;
        }

        .banner_detail_box {
            left: 40%;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }

    @media (max-width: 991px) {

        .full-view-banner,
        .module-sections {
            margin-left: -20px;
            margin-right: -20px;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

        .module-section {
            height: 100px;
        }
    }

    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 86.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {
        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {
        .full-page {
            margin-left: 0px;
            margin-right: 0px;
        }

        .banner {
            height: calc(100vh - 300px);
        }

        .banner_detail_box {
            left: 50%;
        }
    }

    @media (max-width: 575px) {
        div#kt_app_content {
            padding-top: 30px;
        }

        .banner_detail_box {
            width: 70vw !important;
        }

        .full-view-banner {
            margin-top: 0;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(90vw + 90vh) !important;
        }
    }
</style>
