<template>
    <div class="row mt-5">
        <div class="col-md-10 col-lg-8 mx-auto">
            <p class="fs-2">Verify Credential</p>
            <div class="d-flex align-items-center justify-content-center">
                <div class="position-relative w-75 me-2">
                    <Field v-model="searchQuery" class="form-control form-control-solid ps-10" type="text" placeholder="Enter Credential ID" name="scholarshipName" autocomplete="off" />
                </div>
                <div class="d-flex align-items-center w-25">
                    <button @click="searchCredential" :disabled="isLoading"  class="btn btn-secondary w-100 me-5">
                        {{ isLoading ? 'Searching...' : 'Search' }}
                    </button>
                </div>
            </div>
            <div class="mt-20">
                <!-- Error Message -->
                <p v-if="error" class="text-center fs-4">
                    <i class="bi bi-exclamation-circle text-primary fs-3 me-2"></i> {{ error }}
                </p>

                <!-- Results Section -->
                <div class="fs-5" v-if="badgeData">
                    <ul class="nav nav-tabs border-0">
                        <li class="nav-item">
                            <a class="nav-link ps-0" :class="{ active: activeTab === 'badge' }" @click="activeTab = 'badge'" href="javascript:void(0)">Digital Badge</a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link" :class="{ active: activeTab === 'certificate' }" @click="activeTab = 'certificate'" href="javascript:void(0)">View Certificate</a>
                        </li> -->
                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade" :class="{ 'show active': activeTab === 'badge' }">
                            <!-- Badge Content -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center justify-content-between p-10 border border-secondary border-solid rounded">
                                        <!-- Badge Details -->
                                        <div>
                                            <div>
                                                <h1>{{ badgeData.badge.name }}</h1>
                                                <p class="fw-bold mb-5 mt-5" v-if="badgeData.badge.companies">Verified by
                                                     <span v-for=" (company, index) in badgeData.badge.companies" :key="company.id">
                                                            <u>{{ company.name }}</u>
                                                            <span v-if="index !== badgeData.badge.companies.length - 1"> + </span>
                                                     </span>
                                                </p>
                                                
                                            </div>

                                            <div class="mt-10">
                                                <p class="mb-2"><span class="text-gray-700">Issued To: </span>{{ badgeData.badgeable.student.name }}</p>
                                                <p class="mb-2"><span class="text-gray-700">Module Name: </span>{{ badgeData.module_name }}</p>
                                                <p class="mb-2">
                                                    <span class="text-gray-700">Credential ID: </span>{{ badgeData.credential_id }}
                                                </p>
                                                <p class="mb-2">
                                                    <span class="text-gray-700">Issue Date: </span>{{ badgeData.issue_date }}
                                                </p>
                                                <p class="mb-2" v-if="badgeData.expiration_date">
                                                    <span class="text-gray-700">Expiry Date: </span>{{ badgeData.expiration_date }}
                                                </p>
                                                <p class="mb-2">
                                                    <span class="text-gray-700">Module Type: </span>{{ badgeData.module_type }}
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Badge Image -->
                                        <div>
                                            <img v-if="badgeData" :src="badgeData.badge?.image_fullpath" width="200" height="200" />
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" :class="{ 'show active': activeTab === 'certificate' }">
                            <!-- Certificate Content -->
                            <div class="card">
                                <div class="card-body">
                                    <h4>Certificate Details</h4>
                                    <!-- Add your certificate content here -->
                                    <pre>{{ badgeData.certificate }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup>
    import { ref } from 'vue';
    import axios from 'axios';
    import { Field } from "vee-validate";

    const searchQuery = ref('');
    const isLoading = ref(false);
    const error = ref(null);
    const badgeData = ref(null);
    const activeTab = ref('badge');

    const searchCredential = async () => {
        error.value = null;
        badgeData.value = null;
        if (!searchQuery.value) {
            error.value = 'Please enter a credential ID';
            return;
        };

        try {
            isLoading.value = true;
            const response = await axios.post('/api/verify-credential', {
                credential_id: searchQuery.value
            });

            if (response.data.status === 'success') {
                badgeData.value = response.data.badgeKey;
                console.log("badgeData", badgeData);
            } else {
                error.value = response.message || 'Credential not matched';
            }

        } catch (err) {
            error.value = 'An error occurred while verifying the credential';
        } finally {
            isLoading.value = false;
        }
    }
</script>


<style scoped>
    .nav-link {
        color: var(--kt-text-muted);
        border: none;
    }

    .nav-link.active,
    .nav-link:hover,
    .nav-link:active {
        color: #000000;
    }
</style>