<?php

namespace App\Http\Controllers;

use App\ActivityLog;
use App\ChildInvitee;
use App\Country;
use App\Events\LicenseRenewed;
use App\Http\Requests\StoreStudent;
use App\IndividualStudent;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\Nominee;
use App\NonStudentPlan;
use App\Note;
use App\Organisation;
use App\Plan;
use App\Profile;
use App\Role;
use App\School;
use App\Staff;
use App\Standard;
use App\State;
use App\Student;
use App\StudentModuleResult;
use App\StudentTemplateProgress;
use App\Teacher;
use App\UnitFeedback;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use DB;
use Excel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Newsletter;
use App\Http\Requests\StudentImportRequest;
use Illuminate\Support\Facades\Storage;
use App\Imports\StudentImport;
use App\Jobs\ProcessStudentInvitation;
use App\Exports\StudentsExport;
use App\Jobs\NotifyUserOfCompletedStudentsExport;
use App\Services\TimelineService;
use Illuminate\Support\Facades\Auth;
use App\Services\UserAccessService;

class StudentsController extends Controller
{

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (UserAccessService::currentUserCanAccess($request->id)) {
                return $next($request);
            }
            abort(403, 'You do not have permission to perform this action.');
        })->only('notes', 'notesStore', 'edit', 'update');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index()
    {

        // $schools=collect();

        $years = Standard::get();
        $states = State::All();
        $countries = Country::All();
        $campuses = collect();
        if (Auth::user()->isTeacher()) {
            $campuses = Auth::user()->campuses()->get();
        } elseif (Auth::user()->isStaff()) {
            $campuses = Auth::user()->orgCampuses()->get();
        }
        if (Auth::user()->isAdmin()) {
            $schools = School::confirmed()->select('id', 'name')->get();
            $organisations = Organisation::confirmed()->select('id', 'name')->get();
            return view('students.admins.index', compact('schools', 'organisations', 'years', 'states', 'countries'));
        } else {
            return view('students.teachers.index', compact('years', 'states', 'countries', 'campuses'));
        }
    }

    public function getAdvanceFilterData(Request $request)
    {
        $students = User::select('users.id', 'users.name', 'users.email', 'users.role_id', 'users.school_id', 'users.organisation_id', 'users.created_at', 'profiles.user_id', 'profiles.standard_id', 'profiles.graduate_year', 'profiles.school as profile_school', 'profiles.accountcreated', 'profiles.gender as gender', 'profiles.firstname as firstname', 'profiles.lastname as lastname', 'standards.title as year_title')
            ->join('profiles', 'users.id', '=', 'profiles.user_id')
            ->leftJoin('standards', 'profiles.standard_id', '=', 'standards.id')
            ->whereIn('users.role_id', [3, 4])
            ->with('parents', 'sessionStats', 'campuses', 'school.detail:school_id,subscription_ending_on,order_confirmed,confirmed_at,type');

            $viewLimit =  config('profilling.view_limit_job_suggestion');

        // $students = User::select('id', 'name', 'email', 'role_id', 'school_id', 'created_at')->whereIn('users.role_id', [3, 4])->with('parents', 'campuses', 'school.detail:school_id,subscription_ending_on,order_confirmed,confirmed_at', 'profile:user_id,standard_id,school,accountcreated,gender', 'profile.class:id,title');  // school.details:subscription_ending_on,order_confirmed,confirmed_at is selected so that accessor works
        if (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
            $campus = false;
            if (Auth::user()->isTeacher()) {
                $campuses = Auth::user()->campuses()->pluck('campus_id');
            } elseif (Auth::user()->isStaff()) {
                $campuses = Auth::user()->orgCampuses()->pluck('campus_id');
            }
            if ($campuses->count()) {
                if (request('campus')) {
                    $campus = array(request('campus'));
                } else {
                    $campus = $campuses;
                }
            }
            (Auth::user()->isStaff() ? $students->where('users.organisation_id', Auth::user()->organisation_id) : $students->where('users.school_id', Auth::user()->school_id))
                ->where(function ($query) {
                    $query->whereIn('profiles.standard_id', Standard::pluck('id'))  // to get only primary or secondary students
                        ->orWhere(function ($query) {
                            $query->where('standard_id', null)
                                ->where('accountcreated', 0);
                        });
                })
                ->when($campus, function ($query) use ($campus) {
                    $query->where(function ($query) use ($campus) {
                        if (request('campus')) {
                            return $query->whereHas(Auth::user()->isStaff() ? 'orgCampuses' : 'campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            });
                        } else {
                            return $query->whereHas(Auth::user()->isStaff() ? 'orgCampuses' : 'campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            })->orDoesntHave('campuses');
                        }
                    });
                })
                ->when(session('schoolSection') == 'primary', function ($query) {
                    return $query->whereHas("profile", function ($query) {
                        $years = Standard::primarySchool()->pluck('id');
                        $query->whereIn('standard_id', $years);
                    });
                })
                ->when(session('schoolSection') == 'secondary', function ($query) {
                    return $query->whereHas("profile", function ($query) {
                        $years = Standard::secondarySchool()->pluck('id');
                        $query->whereIn('standard_id', $years)->orWhere(function ($query) {
                            $query->where('standard_id', null)
                                ->where('accountcreated', 0);
                        });
                    });
                });
        }
        $datatables = app('datatables')->eloquent($students)
            ->filter(function ($query) use ($viewLimit) {
                if ($name = request('name')) {
                    $query->where('name', 'like', "%{$name}%");
                }
                if ($email = request('email')) {
                    $query->where('email', 'like', "%{$email}%");
                }
                if ($school = request('schoolname')) {
                    $query->where('school_id', $school);
                }
                if (request('schoolname') && request('campus')) {
                    $query->whereHas('campuses', function ($q) {
                        $q->where('campus_id', request('campus'));
                    });
                }
                if ($schoolType = request('type')) {
                    $query->whereHas('school.detail', function ($q) use ($schoolType) {
                        $q->where('type', $schoolType);
                    });
                }
                if ($schoolStateId = request('state')) {
                    $query->whereHas('school', function ($q) use ($schoolStateId) {
                        $q->where('state_id', $schoolStateId);
                    });
                }
                if ($organisation = request('orgname')) {
                    $query->where('organisation_id', $organisation);
                }
                if (request('orgname') && request('orgCampus')) {
                    $query->whereHas('orgCampuses', function ($q) {
                        $q->where('campus_id', request('orgCampus'));
                    });
                }
                if ($school = request('created_in')) {
                    $query->whereYear('users.created_at', request('created_in'));
                }
                if ($gender = request('gender')) {
                    if ($gender == 'M' || $gender == 'F') {
                        $query->whereHas('profile', function ($q) use ($gender) {
                            $q->whereGender($gender);
                        });
                    } else {
                        $query->whereHas('profile', function ($q) use ($gender) {
                            $q->whereNotIn('gender', ['M', 'F']);
                        });
                    }
                }
                if (request('email')) {
                    $query->where('users.email', "LIKE", "%" . request('email') . "%");
                }
                if ($registrationstatus = request('registration')) {
                    $query->whereHas('profile', function ($q) use ($registrationstatus) {
                        $status = ($registrationstatus == "completed") ? 1 : 0;
                        $q->where('accountcreated', $status);
                    });
                }
                if (request('profiler')) {
                    if (request('profiler') == 'completed') {
                        $query->whereHas('profilerResult');
                    } elseif (request('profiler') == 'incomplete') {
                        $query->whereDoesntHave('profilerResult');
                    }
                }
                if ($videoProfiling = request('video_profiling')) {
                    if (!in_array($videoProfiling, ['completed', 'in_progress', 'incomplete'])) {
                        return;
                    }
                    $query->where(function ($subQuery) use ($videoProfiling, $viewLimit) {
                        if ($videoProfiling === 'completed') {
                            $subQuery->whereHas('profilingPostResponses', function ($q) use ($viewLimit) {
                                $q->where('timespent', '>', 0)
                                  ->havingRaw('COUNT(*) >= ?', [$viewLimit]);
                            })->whereHas('userSelectedOccupations');
                        } elseif ($videoProfiling === 'in_progress') {
                            $subQuery->whereHas('profilingPostResponses', function ($q) use ($viewLimit) {
                                $q->where('timespent', '>', 0)
                                  ->havingRaw('COUNT(*) > 0 AND COUNT(*) < ?', [$viewLimit]);
                            })->orWhere(function ($q) {
                                $q->whereHas('profilingPostResponses', function ($q2) {
                                    $q2->where('timespent', '>', 0);
                                })->whereDoesntHave('userSelectedOccupations');
                            });
                        } elseif ($videoProfiling === 'incomplete') {
                            $subQuery->whereDoesntHave('profilingPostResponses', function ($q) {
                                $q->where('timespent', '>', 0);
                            });
                        }
                    });
                }
                if (request('status') == 'active') {
                // if (Auth::user()->isAdmin()) {

                // $query->where(function ($query) {
                    $query->where("license_end_date", ">", Carbon::now())/* ->orWhere(function ($query) {
                        $query->where('accountcreated', 0);
                    }); */
                /* }) */->whereHas('profile', function ($q) {
                   $q->where('standards.title', '<>', 'I’ve finished high school')->whereRemoved(false);
                });
                    // } else {
                    //     $query->whereHas('profile', function ($q) {
                    //         // if ($year = request('year')) {
                    //         //     $q->where('standard_id', '<>', 7)->whereStandardId($year)->whereRemoved(false);
                    //         // } else {
                    //         $q->where(function ($query) {
                    //             $query->where('standard_id', '<>', 7)
                    //                 ->whereRemoved(false);
                    //         })->orWhere(function ($query) {
                    //             $query->where('standard_id', null)
                    //                 ->where('accountcreated', 0);
                    //         });
                    //         // }
                    //     });
                    // }

                    if ($year = request('year')) {
                        $query->whereHas('profile', function ($q) use ($year) {
                            $q->whereStandardId($year);
                        });
                    }
                } elseif (request('status') == 'graduated') {
                    $query->whereHas('profile', function ($q) {
                        if ($year = request('graduate_year')) {
                            $q->where('standard_id', 7)->whereGraduateYear($year)->whereRemoved(false);
                        } else {
                            $q->where('standard_id', 7)->whereRemoved(false);
                        }
                    });
                } elseif (request('status') == 'inactive') {

                    $query->where(function ($query) {
                        $query->where("license_end_date", "<", Carbon::now())->orWhereHas('profile', function ($q) {
                            $q->whereRemoved(true);
                        });
                    });
                    // if (Auth::user()->isAdmin()) {
                    //     $query->where("license_end_date", "<", Carbon::now());
                    // } else {
                    //     $query->whereHas('profile', function ($q) use ($gender) {
                    //         $q->whereRemoved(true);
                    //     });
                    // }

                    if ($year = request('year')) {
                        $query->whereHas('profile', function ($q) use ($year) {
                            $q->whereStandardId($year);
                        });
                    }
                } elseif (request('status') == 'pending') {

                    $query->whereHas('profile', function ($q) {
                            $q->whereAccountcreated(false);
                        });
                    // if (Auth::user()->isAdmin()) {
                    //     $query->where("license_end_date", "<", Carbon::now());
                    // } else {
                    //     $query->whereHas('profile', function ($q) use ($gender) {
                    //         $q->whereRemoved(true);
                    //     });
                    // }

                }
                // if (request('active_inactive') == 'Active') {
                //     //
                // } elseif (request('active_inactive') == 'Inactive') {
                //     //
                // }>
                // if ($year = request('year')) {
                //     $query->where('standards.id', $year);
                // }
                // if ($accountcreated = request('accountcreated')) {
                //     $query->where('accountcreated', $accountcreated);
                // }
            }, true);
        return $datatables
            ->addColumn('select', '<div class="checkbox check-primary my-0"> <input type="checkbox"  name="ids[]" value="{{$id}}" id="select{{$id}}"> <label for="select{{$id}}"></label></div>')
            ->addColumn('schoolname', function (User $user) {
                if (!$user->school) {
                    return $user->profile_school;
                }
                return $user->school->name;
            })
            ->addColumn('state', function (User $user) {
                return $user->school->state->code ?? 'Unassigned';
            })
            ->addColumn('type', function (User $user) {
                return $user->school->detail->type ?? 'Unassigned';
            })
            ->addColumn('schoolCampus', function (User $user) {
                if (Auth::user()->isStaff()) {
                    return @$user->orgCampuses()->first()->name ?? 'Unassigned';
                } else {
                    return @$user->campuses()->first()->name ?? 'Unassigned';
                }
            })
            ->addColumn('orgname', function (User $user) {
                return $user->organisation->name ?? 'Unassigned';
            })
            ->addColumn('orgCampus', function (User $user) {
                return @$user->orgCampuses()->first()->name ?? 'Unassigned';
            })
            // ->addColumn('year', function (User $user) {
            //     return @$user->profile->class->title;
            // })
            // ->addColumn('p_gender', function (User $user) {
            //     return $user->profile->gender;
            // })
            ->addColumn('tags', function (User $user) {
                // if ($user->is_child && $user->belongs_to_school) {
                //     return '<span class="label">Child</span><span class="hidden">, </span><span class="label">Student</span>';
                // } elseif ($user->is_child && !$user->belongs_to_school) {
                //     return '<span class="label">Child</span>';
                // } elseif (!$user->is_child && $user->belongs_to_school) {
                //     return '<span class="label">Student</span>';
                // } elseif ($user->is_individual && $user->belongs_to_school) {
                //     return '<span class="label">Individual</span>';
                // } elseif ($user->is_individual && !$user->belongs_to_school) {
                //     return '<span class="label">Individual</span><span class="hidden"></span>';
                // }

                $tag = '';
                if ($user->is_individual) {
                    $tag .= '<span class="label">Individual</span>';
                } /* else { */
                if ($user->is_child) {
                    $tag .= '<span class="label">Child</span><span class="hidden">, </span>';
                }
                if ($user->belongs_to_school && !$user->profile->removed) {
                    $tag .= '<span class="label">Student</span><span class="hidden">, </span>';
                }
                // }

                return $tag;
            })
            ->addColumn('parents', function (User $user) {
                if ($user->is_child) {
                    $data = '';
                    // here we prepare the options
                    foreach ($user->parents as $key => $parent) {
                        $data .= '<span class="label label-inverse mb-1">' . $parent->name . '</span>' . ($key + 1 == $user->parents->count() ? "" : "<span class='hidden'>,</span> ");
                    }
                    return $data;
                }
            })
            ->addColumn('p_accountcreated', function (User $user) {
                if ($user->profile->accountcreated) {
                    return '<i class="fa fa-check text-success"><span class="hidden">Yes</span></i>';
                } else {
                    return '<i class="fa fa-times text-danger"><span class="hidden">No</span></i>';
                }
            })
            ->addColumn('profiling', function (User $user) {
                if ($user->has_completed_profiler) {
                    if (Auth::user()->isAdmin()) {
                        return '<a href="students/' . $user->id . '/profiling" class="fs-16"> <i class="fa fa-eye"></i></a><span class="hidden">Completed</span>';
                    } else {
                        return 'Completed <i class="fa fa-check text-success"></i>';
                    }
                } else {
                    return 'Incomplete <i class="fa fa-times text-danger"></i>';
                }
            })
            ->addColumn('video_profiling', function (User $user) {
                if ($user->video_profiling_status === 'Completed') {
                    return 'Completed <i class="fa fa-check text-success"></i>';
                } elseif ($user->video_profiling_status === 'Incomplete') {
                    return 'Incomplete <i class="fa fa-times text-danger"></i>';
                } elseif ($user->video_profiling_status === 'In Progress') {
                    return 'InProgress <i class="fa fa-spinner fa-spin text-warning"></i>';
                } else {
                    return 'N/A <i class="fa fa-question text-muted"></i>';
                }
            })

            ->addColumn('profile', '<a href="profiles/edit/{{$id}}" class="fs-16"> <i class="fa fa-eye"></i></a>')
            ->addColumn('gameplan', '<a href="students/{{$id}}/gameplan" class="fs-16"> <i class="fa fa-eye"></i></a>')
            ->addColumn('timeline', '<a href="students/{{$id}}/timeline" class="fs-16"> <i class="fa fa-eye"></i></a>')
            ->addColumn('notes', function (User $user) {
                if ($user->school_id) {
                    return '<a href="students/' . $user->id . '/notes" class="fs-16"> <i class="fa fa-eye"></i></a>';
                } else {
                    return 'NA';
                }
            })
            ->addColumn('cvs', function (User $user) {
                if ($user->has_cv) {
                    return '<a href="students/' . $user->id . '/cvs" class="fs-16"> <i class="fa fa-eye"></i></a><span class="hidden">Completed</span>';
                } else {
                    return 'NA';
                }
            })
            ->addColumn('lastseen', function (User $user) {
                // return $user->last_seen;
                return $user->sessionStats ? Carbon::parse($user->sessionStats->last_seen)->toDayDateTimeString() : '-';
            })
            ->addColumn('logincount', function (User $user) {
                // return $user->login_count;
                return $user->sessionStats ? $user->sessionStats->login_count : '-';
            })
            ->addColumn('avgduration', function (User $user) {
                // return $user->avg_session_duration ? CarbonInterval::seconds(round($user->avg_session_duration))->cascade()->forHumans(['short' => true]) : '-';
                return $user->sessionStats ? CarbonInterval::seconds(round($user->sessionStats->avg_duration))->cascade()->forHumans(['short' => true]) : '-';
            })
            ->addColumn('avgdurationsec', function (User $user) {
                // return $user->avg_session_duration ? $user->avg_session_duration : '-';
                return $user->sessionStats ? $user->sessionStats->avg_duration : '-';
            })
            ->addColumn('account_created', function (User $user) {
                return Carbon::parse($user->created_at)->toFormattedDateString();
            })
            ->addColumn('subscription_end_date', function (User $user) {
                if ($user->subscription_end_date) {
                    return Carbon::parse($user->subscription_end_date)->toFormattedDateString();
                }
                return '';
            })
            ->addColumn('action', function (User $user) {
                if (Auth::user()->isAdmin()) {
                    if ($user->role_id == 4) {
                        // return '<a href="students/'.$user->id.'" data-method="delete" data-token="'.csrf_token().'" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';>
                        return '<a data-target="#modalChildEdit" data-id="' . $user->id . '" data-toggle="modal"  class="" href="#"> <i class="fa fa-edit"></i></a> <a href="students/' . $user->id . '" data-method="delete" data-token="' . csrf_token() . '" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';
                    }
                    return '<a href="students/' . $user->id . '/invite"> <i class="fa fa-paper-plane-o text-danger" data-toggle="tooltip" title="Resend welcome email."></i></a><a data-target="#modalStudentEdit" data-id="' . $user->id . '" data-toggle="modal" class="" href="#"> <i class="fa fa-edit"></i></a> <a href="students/' . $user->id . '" data-method="delete" data-token="' . csrf_token() . '" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';
                } elseif (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
                    return '<a href="students/' . $user->id . '/invite"> <i class="fa fa-paper-plane-o text-danger" data-toggle="tooltip" title="Resend welcome email."></i></a><a data-target="#modalTeacherStudentEdit" data-id="' . $user->id . '" data-toggle="modal"  class="" href="#"> <i class="fa fa-edit"></i></a>';
                    // return '<a href="students/' . $user->id . '" data-method="delete" data-token="' . csrf_token() . '" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';
                }
                // return '<a href="students/' . $user->id . '/remove" class="removeStudent" data-toggle="tooltip" title="Remove student"> <i class="fa fa-ban text-danger"></i></a>';
            })
            ->rawColumns(['select', 'schoolname', 'type', 'state', 'schoolCampus', 'orgname', 'orgCampus', 'year', 'gender', 'tags', 'parents', 'p_accountcreated', 'profiling', 'video_profiling', 'profile', 'gameplan', 'timeline', 'notes', 'cvs', 'lastseen', 'logincount', 'avgduration', 'avgdurationsec', 'subscription_end_date', 'action'])
            ->make(true);
    }

    public function import($school = '') // not used
    {
        // $schools = School::select('id', 'name')->get();

        if (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
            return view('students.import');
        } else {
            $school = School::where('id', $school)->value('name');

            return view('students.import', compact('school'));
        }
    }

    // public function importStudent(Request $request, $school = '')
    // {
    //     try {
    //         Excel::load($request->file('import_file'), function ($reader) use ($school) {
    //             if (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
    //                 $teacher = Auth::user();
    //                 $schoolid = $teacher->school_id;
    //             } else {
    //                 $schoolid = $school;
    //             }

    //             foreach ($reader->toObject() as $student) {
    //                 if ($student->firstname && $student->lastname) {
    //                     $year = Standard::where('title', $student->year)->value('id');
    //                     // if (School::hasSpace($schoolid, $year)) {
    //                     if ($student->email) {
    //                         $emailexist = User::where('email', $student->email)->exists();
    //                     } else {
    //                         $emailexist = false;
    //                     }
    //                     if (!$emailexist) {
    //                         $newStudent = new Student;
    //                         $newStudent->role_id = 3;
    //                         $newStudent->school_id = $schoolid;
    //                         $newStudent->name = $student->firstname . " " . $student->lastname;
    //                         $newStudent->email = $student->email ? $student->email : Str::random(40);
    //                         $newStudent->save();
    //                         if ($newStudent) {
    //                             $profile = new Profile;
    //                             $profile->firstname = $student->firstname;
    //                             $profile->lastname = $student->lastname;
    //                             $profile->gender = $student->gender;
    //                             $profile->standard_id = $year;

    //                             $newStudent->profile()->save($profile);

    //                             // if ($newStudent->email && filter_var($newStudent->email, FILTER_VALIDATE_EMAIL)) {
    //                             //     User::addHelpcrunchAccount($newStudent->id);
    //                             // }
    //                         }
    //                     }
    //                     // }
    //                 }
    //             }
    //         });
    //         $redirectUrl = url()->previous();
    //         if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
    //             $redirectUrl = session()->get('previousUrl');
    //         }
    //         return redirect($redirectUrl)->with('message', "Students imported successfully");
    //     } catch (\Exception $e) {
    //         $redirectUrl = url()->previous();
    //         if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
    //             $redirectUrl = session()->get('previousUrl');
    //         }
    //         return redirect($redirectUrl)->with('error', $e->getMessage());
    //     }
    // }

    public function downloadsample($campus = null)
    {
        // if ($campus) {
        //     return Storage::download('studentssamplewithcampus.xlsx');
        // }
        // return Storage::download('studentssample.xlsx');
        if ($campus) {
            return Storage::download('samples/csv/StudentImportCampus.csv');
        }
        return Storage::download('samples/csv/StudentImport.csv');
    }

    public function importStudents(StudentImportRequest $request)
    {
        if ($request->hasFile('studentsfile')) {
            $exceldirectory = "excel";
            if (!Storage::exists($exceldirectory)) {
                Storage::makeDirectory($exceldirectory, 0777);
            }
            if (Auth::user()->isTeacher()) {
                $school = Auth::user()->school_id;
            } else {
                $school = $request->school;
            }
            Log::info("Attempting student import for school $school");
            $sendmail = $request->welcome_email;

            if (!School::whereId($school)->exists()) {
                return back()->with('message', 'Invalid school selected for import.');
            }

            try {
                Excel::import(new StudentImport($school, $sendmail, Auth::id(), []), $request->file('studentsfile'));
            } catch (\Throwable $th) {
                Log::error($th);
            }

            return redirect()->route("students.index")->with("message", "We are processing your import. You will soon be able to see these users in your student list. If the option was selected, they will each receive an email to set up their account; you can also send this email to students using options below.");
        }
        return redirect()->route("students.index")->with("message", "Please make sure your uploaded file matches the sample file format");
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreStudent $request)
    {
        if (Auth::user()->isTeacher()) {
            $schoolid = Auth::user()->school_id;
            $organisationid = null;
        } elseif (Auth::user()->isStaff()) {
            $schoolid = null;
            $organisationid = Auth::user()->organisation_id;
        } elseif (Auth::user()->isAdmin()) {
            $schoolid = request('school');
            $organisationid = request('organisation');
        }
        // if (School::hasSpace($schoolid, request('year'))) {
        $roleId = Role::where('name', "Student")->value('id');

        $student = Student::Create([
            'name' => request('firstname') . " " . request('lastname'),
            'email' => (request('email')) ? request('email') : Str::random(40),
            'role_id' => $roleId,
            'password' => (request('password')) ? bcrypt(request('password')) : Str::random(10),
            'school_id' => $schoolid,
            'organisation_id' => $organisationid,
            'state_id' => request('state'),
            'postcode' => request('postcode'),
        ]);

        if (request('other_gender')) {
            $gender = request('other_gender');
        } else {
            $gender = request('gender');
        }

        $profile = new Profile;
        $profile->firstname = request('firstname');
        $profile->lastname = request('lastname');
        $profile->gender = $gender;
        $profile->standard_id = request('year');
        $student->profile()->save($profile);

        $student->campuses()->sync($request->campus);
        $student->orgCampuses()->sync($request->orgCampus);

        // if ($student->email && filter_var($student->email, FILTER_VALIDATE_EMAIL)) {
        //     User::addHelpcrunchAccount($student->id);
        // }
        if (request('welcome_email') == 1) {
            dispatch((new ProcessStudentInvitation($student)));
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Student has been added successfully!');
        // }

        // $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Oops! Student registration has failed because maximum number of students has already been registered for year' . request('year') . '!');
    }

    public function teacherStudentAdd(StoreStudent $request)
    {
        $schoolid = Auth::user()->school_id;
        $roleId = Role::where('name', "Student")->value('id');

        $student = Student::Create([
            'name' => request('firstname') . " " . request('lastname'),
            'email' => (request('email')) ? request('email') : Str::random(40),
            'role_id' => $roleId,
            'password' => (request('password')) ? bcrypt(request('password')) : Str::random(10),
            'school_id' => $schoolid,
        ]);


        $profile = new Profile;
        $profile->firstname = request('firstname');
        $profile->lastname = request('lastname');
        $profile->gender = request('gender');
        $profile->standard_id = request('year');
        $profile->graduate_year = (request('year') == 7) ? request('graduate_year') : null;
        $profile->year_popup = false;
        $student->profile()->save($profile);

        if (request('campus')) {
            $campuses = request('campus');
            $student->campuses()->attach($campuses);
        }

        // if ($student->email && filter_var($student->email, FILTER_VALIDATE_EMAIL)) {
        //     User::addHelpcrunchAccount($student->id);
        // }

        if (request('welcome_email') == 1) {
            dispatch((new ProcessStudentInvitation($student)));
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Student has been added successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Student  $student
     * @return \Illuminate\Http\Response
     */
    public function show(Student $student)
    {
        if (request()->wantsJson()) {
            $student->country = @$student->state->country->id;
            $student->firstname = $student->profile->firstname;
            $student->lastname = $student->profile->lastname;
            $student->gender = $student->profile->gender;
            $student->year = $student->profile->standard_id;
            $student->graduate_year = $student->profile->graduate_year;
            $student->removed = $student->profile->removed;
            $student->parents;
            $student->campus_id = @$student->campus->id;
            $student->orgCampus_id = @$student->orgCampus->id;
            return ($student);
        }
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Student  $student
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {}

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Student  $student
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $student = Student::find($id);
        if (Auth::user()->isAdmin()) {
            $oldEmail = $student->email;
            $oldYear = $student->profile->standard_id;

            $student->name = request('firstname') . " " . request('lastname');
            $student->email = (request('email')) ? request('email') : Str::random(40);
            if ($request->password) {
                $student->password = bcrypt($request->password);
            }
            $student->state_id = request('state');
            $student->postcode = request('postcode');
            if (Auth::user()->isAdmin()) {
                $student->school_id = request('school');
                $student->organisation_id = request('organisation');
            }


            $profile = $student->profile;
            $profile->firstname = request('firstname');
            $profile->lastname = request('lastname');

            $profile->gender = request('gender');
            $profile->year_popup = $profile->standard_id != request('year');
            $profile->standard_id = request('year');
            $profile->graduate_year = (request('year') == 7) ? request('graduate_year') : null;
            $oldremoved = $profile->removed;
            $profile->removed = $request->removed;


            $student->save();
            $student->profile()->save($profile);
            $student->campuses()->sync($request->campus);
            $student->orgCampuses()->sync($request->orgCampus);

            $oldParents = $student->parents->pluck('id')->toArray();
            $newParents = $request->parents;
            $toRemove = [];
            if ($oldParents && $newParents) {
                $toRemove = array_diff($oldParents, $newParents);
            }
            if ($toRemove) {
                ChildInvitee::where('child_id', $student->id)->whereIn('parent_id', $toRemove)->delete();
            }
            \Event::dispatch(new LicenseRenewed(User::where("id", $id)->first()));
            $student->parents()->sync($newParents);
        } else {
            $oldEmail = $student->email;
            $oldYear = $student->profile->standard_id;

            $student->name = request('firstname') . " " . request('lastname');
            $student->email = request('email');
            if ($request->password) {
                $student->password = bcrypt($request->password);
            }
            $profile = $student->profile;
            $profile->firstname = request('firstname');
            $profile->lastname = request('lastname');
            $profile->gender = request('gender');
            $profile->year_popup = $profile->standard_id != request('year');
            $profile->standard_id = request('year');
            $profile->graduate_year = (request('year') == 7) ? request('graduate_year') : null;
            $student->save();
            $student->profile()->save($profile);
            if (Auth::user()->isTeacher()) {
                $student->campuses()->sync($request->campus);
            } else {
                $student->orgCampuses()->sync($request->orgCampus);
            }
        }


        if ($student->email && filter_var($student->email, FILTER_VALIDATE_EMAIL)) {
            // User::addHelpcrunchAccount($student->id);
            if ($student->profile->accountcreated) {
                if (($oldEmail != $student->email) || ($oldYear != $student->profile->year)) {
                    if ($oldEmail != $student->email) {
                        // Newsletter::updateEmailAddress($oldEmail, $request->email, 'students');
                    }
                    // User::updateAssociatedUsersMailchimpDetail($student->id);
                }
                $user = User::find($student->id);
                // UpdateMailchimpAudienceJob::dispatch($user);
            }
        }
        // if (School::hasSpace($schoolid, request('year'))) {
        // dd(filter_var($student->email, FILTER_VALIDATE_EMAIL) != false);
        Cache::tags('user.' . $student->id)->flush();
        Cache::tags(['profile' . $student->id])->flush();
        Cache::forget('profile' . $student->id);
        Cache::forget('location' . $student->id);
        Cache::forget('belongs_to_school' . $student->id);
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Student has been updated successfully!');
        // }

        // $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Oops! Student updation has failed because maximum number of student has already been registered for year' . request('year') . '!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Student  $student
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (UserAccessService::currentUserCanAccess($id)) {
            $student = IndividualStudent::find($id);
            if (!$student) {
                $student = Student::find($id);
            }
            $delete = $student->delete();

            if ($delete) {
                User::deleteHelpcrunchAccount($student->id);
            }
            return redirect('students')->with('message', 'Student has been deleted successfully!');
        }
        abort(403, 'You do not have permission to perform this action.');
    }


    public function timelineOld($id = "")
    {
        $breadcrumb = "students-timeline";
        $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id)) ? "Timeline" : "Your Timeline";

        if (Auth::check() && (Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && (session('studentView') || $id)))) {
            if (!$id) {
                $id = Auth::id();
            }
        } elseif (!($id && (User::findOrfail($id)->isStudent()) && UserAccessService::currentUserCanAccess($id))) {
            abort(403, 'You do not have permission to perform this action.');
        }
        $individual = IndividualStudent::find($id);
        if ($individual) {
            // if ($individual->isNonStudent()) {
            //     $plans = NonStudentPlan::where('user_id', $id)->select('id', 'created_at')->latest()->get();
            //     $nonStudent = true;
            //     if (!$plans) {
            //         $plans = Plan::where('user_id', $id)->select('id', 'created_at')->latest()->get();
            //         $nonStudent = false;
            //     }
            // } else {
            //     $plans = Plan::where('user_id', $id)->select('id', 'created_at')->latest()->get();
            //     $nonStudent = false;
            //     if (!$plans) {
            //         $plans = NonStudentPlan::where('user_id', $id)->select('id', 'created_at')->latest()->get();
            //         $nonStudent = true;
            //     }
            // }
            $plans = Plan::where('user_id', $id)->select(['id', 'created_at', DB::raw("'plan' as type")])/* ->latest()->get() */;
            $nonStudent = false;
            $sName = $individual->name;
        } else {
            $plans = Plan::where('user_id', $id)->select(['id', 'created_at', DB::raw("'plan' as type")])/* ->latest()->get() */;
            $nonStudent = false;
            $sName = Student::where('id', $id)->value('name');
        }

        $progress = StudentTemplateProgress::where([
            'student_id' => $id,
            'status' => 'completed',
        ])->select(['id', 'created_at', DB::raw("'unit' as type")])->has('template')/* ->latest()->get() */;

        // dd($progress);

        $timeline = collect();
        // foreach ($progress as $key => $value) {
        //     $progress[$key]->type = 'unit';
        // $timeline->push($progress);
        // }
        // foreach ($plans as $key => $value) {
        //     $plans[$key]->type = 'plan';
        // $timeline->push($plans);
        // }

        $profiling = StudentModuleResult::whereUserId($id)->select(['id', 'created_at', DB::raw("'profiling' as type")]);

        // if ($profiling) {
        // //     $profiling->type = 'profiling';
        //     $timeline->push($profiling);
        // }

        // $timeline = $timeline->sortByDesc('created_at')->values();

        // dd($plans->union($progress)->union($profiling)->latest()->limit(20)->pluck('type', 'id'));
        // dd($timeline->collapse());

        $timeline = $plans->union($progress)->union($profiling)->latest()->get();
        $name = $sName;
        return view('students.timeline', compact('timeline', 'nonStudent', 'sName', 'breadcrumb', 'bannerTitle', 'id', 'name'));
    }

    /**
     * Get Activity Timeline of student
     *
     * @param \Illuminate\Http\Request $request
     * @param string $id
     * @return mixed
     */
    public function timeline(Request $request, TimelineService $timelineService, $id = "")
    {
        // dd("dfadsf");
        $breadcrumb = "students-timeline";
        $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id) || (Auth::user()->isEmployer() && $id)) ? "Timeline" : "Your Timeline";

        if (Auth::check() && (Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff() || Auth::user()->isEmployer()) && (session('studentView') || $id)))) {
            if (!$id) {
                $id = Auth::id();
            }
        } elseif (!($id && (User::findOrfail($id)->isStudent()) && UserAccessService::currentUserCanAccess($id))) {
            abort(401, 'You do not have permission to perform this action.');
        }

        $individual = IndividualStudent::find($id);

        if ($individual) {
            $nonStudent = false;
            $sName = $individual->name;
        } else {
            $nonStudent = false;
            $sName = Student::where('id', $id)->value('name');
        }

        $name = $sName;

        $timeline = ActivityLog::with(['subject', 'causer'])
            ->where('log_name', 'timeline')
            ->where('causer_type', User::class)
            ->where('causer_id', $id)
            ->latest()
            ->paginate(25);


        if ($request->ajax()) {
            return response()->json([
                'html' => view('students.timeline-data', compact('timeline', 'nonStudent', 'timelineService'))->render()
            ]);
        }

        return view('students.timeline2', compact('timeline', 'nonStudent', 'sName', 'breadcrumb', 'bannerTitle', 'id', 'name', 'timelineService'));
    }


    public function notes($id)
    {
        if (session('studentView') == 'true' && Auth::user()->isTeacher()) {
            $student = Teacher::find($id);
            $breadcrumb = "students-notes";
            $bannerTitle = "Your Notes";
        } elseif (session('studentView') == 'true' && Auth::user()->isStaff()) {
            $student = Staff::find($id);
            $breadcrumb = "students-notes";
            $bannerTitle = "Your Notes";
        } else {
            $breadcrumb = "students-notes";
            $bannerTitle = "Notes";
            $student = Student::find($id);
            $name = $student->name;
        }

        if (\Auth::check() && !(\Auth::user()->isAdmin())) {
            if (Auth::user()->isStaff()) {
                $colleagues = Staff::whereOrganisationId(Auth::user()->organisation_id)->pluck('id');
            } else {
                $colleagues = Teacher::whereSchoolId(Auth::user()->school_id)->pluck('id');
            }

            $notes = $student->notes()->where(function ($query) use ($colleagues) {
                $query->where(function ($q1) use ($colleagues) {
                    $q1->where('private', false)->whereIn('added_by', $colleagues);
                })->orWhere('added_by', Auth::id());
            })->latest()->get();
        } elseif (\Auth::check()) {
            $notes = $student->notes()->latest()->get();
        }

        if (session('studentView') == 'true' || (Auth::user()->isTeacher() && $id)) {
            return view('students.notes', compact('student', 'notes', 'breadcrumb', 'bannerTitle', 'id', 'name'));
        } else {
            return view('students.notes', compact('student', 'notes'));
        }
    }

    public function notesStore($id, Request $request)
    {
        if (($request->description && $request->description != '<p><br></p>') || $request->attachment) {

            $note = new Note;
            $note->user_id = $id;
            $note->added_by = Auth::id();
            $note->description = $request->description;
            $note->private = $request->private ?? false;

            $note->save();

            if ($request->attachment) {
                $uploadpath = $request->attachment->store('attachments', ['visibility' => 'public']);
                $note->attachment()->create([
                    'path' => $uploadpath,
                    'name' => $request->attachment->getClientOriginalName(),
                ]);
            }

            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Note has been added successfully!');
        } else {
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('error', 'Your note is empty!');
        }
    }

    public function notesShow($id)
    {
        if (Auth::user()->isStaff()) {
            $colleagues = Staff::whereOrganisationId(Auth::user()->organisation_id)->pluck('id');
        } else {
            $colleagues = Teacher::whereSchoolId(Auth::user()->school_id)->pluck('id');
        }
        $note = Note::where(function ($query) use ($colleagues) {
            $query->where(function ($q1) use ($colleagues) {
                $q1->where('private', false)->whereIn('added_by', $colleagues);
            })->orWhere('added_by', Auth::id());
        })->with('attachment')->findOrFail($id);

        // if($note->attachment) {
        //     $note->attachment->full_path = $note->attachment->full_path;
        // }

        return $note;
    }

    public function notesUpdate($id, Request $request)
    {
        $note = Note::findOrFail($id);
        if (Auth::user()->isAdmin() || $note->added_by == Auth::id()) {
            $note->description = $request->description;
            $note->private = $request->private ?? false;

            $note->save();

            if ($request->new_attachment) {
                $note->attachment()->delete();
                $uploadpath = $request->new_attachment->store('attachments', ['visibility' => 'public']);
                $note->attachment()->create([
                    'path' => $uploadpath,
                    'name' => $request->new_attachment->getClientOriginalName(),
                ]);
            } elseif (!$request->new_attachment && !$request->old_attachment) {
                $note->attachment()->delete();
            }

            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Note has been updated successfully!');
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function notesDestroy($id)
    {
        $note = Note::findOrFail($id);
        if (Auth::user()->isAdmin() || $note->added_by == Auth::id()) {
            $note->delete();
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Note has been deleted successfully!');
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function bulkAction(Request $request)
    {
        $profile = Profile::whereIn('user_id', $request->ids);
        if ($request->withselected == "Change year") {
            $profile->update([
                'standard_id' => $request->standard,
                'year_popup' => true,
                'graduate_year' => $request->bulk_graduated_year ?? null,
            ]);
            $message = 'Students year updated successfully!';
            Cache::tags(['profile'])->flush();
        } elseif ($request->withselected == "Make Active/Inactive") {
            $profile->update([
                'removed' => $request->remove,
            ]);
            $message = 'Students activation changed successfully!';
            foreach ($request->ids as $key => $id) {
                \Event::dispatch(new LicenseRenewed(User::where("id", $id)->first()));
            }
            Cache::tags(['profile'])->flush();
        } elseif ($request->withselected == "Resend Welcome Email") {
            $students = Student::whereIn('id', $request->ids)->whereHas('profile', function ($query) {
                $query->where('accountcreated', '<>', 1);
            })->get();
            foreach ($students as $key => $student) {
                dispatch((new ProcessStudentInvitation($student)));
            }
            $message = 'Welcome emails will be sent shortly to the selected students.';
        } elseif ($request->withselected == "Reset Profiling") {
            $message = 'Students profile reset successfully!';
            foreach ($request->ids as $id) {
                if (UserAccessService::currentUserCanAccess($id)) {
                    if ($request->nominees == 'yes') {
                        Nominee::whereStudentId($id)->delete();
                    }
                    StudentModuleResult::whereUserId($id)->delete();
                    UnitFeedback::whereUserId($id)->delete();
                    Cache::forget('hasCompletedProfiler' . $id);
                }
            }
        } elseif (Auth::user()->isAdmin() && $request->withselected == 'Delete') {
            foreach ($request->ids as $id) {
                $student = IndividualStudent::find($id);
                if (!$student) {
                    $student = Student::find($id);
                }
                $delete = $student->delete();

                if ($delete) {
                    User::deleteHelpcrunchAccount($student->id);
                }
            }
            $message = 'Students deleted successfully!';
        }

        $request->flashExcept(['ids', 'standard']);
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', $message);
    }

    public function remove($id)
    {
        $student = Student::find($id);
        $student->role_id = 4;
        $student->school_id = null;

        $profile = $student->profile;
        $profile->removed = true;

        $student->save();
        $student->profile()->save($profile);
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', '"' . $student->name . '" has been removed successfully from the school!');
    }

    public function export(Request $request)
    {
        $filename = 'exports/students-' . time() . '.xlsx';
        $user = Auth::user();
        $schoolSection = session('schoolSection');
        (new StudentsExport($request->all(), $user, $schoolSection))->queue($filename)->chain([
            new NotifyUserOfCompletedStudentsExport(request()->user(), $filename),
        ]);

        return redirect('/students')->with('message', "Export is in process, you will get an email with download link once it is ready.");
    }

    // public function yearRollover(Request $request)
    // {

    //     DB::beginTransaction();

    //     try {
    //         $date = Carbon::now();
    //         $year = $date->year;
    //         $prevYear = $date->subYear()->year;
    //         $graduate = Profile::where('standard_id', 6)->whereAccountcreated(true)->whereHas('user', function ($q) use ($year) {
    //             $q->whereIn('role_id', Role::whereIn('name', ['Individual Student', 'Student'])->pluck('id'))->whereYear('created_at', '<', $year);
    //         });

    //         $student = Profile::where('standard_id', '<>',  7)->whereAccountcreated(true)->whereHas('user', function ($q) use ($year) {
    //             $q->whereIn('role_id', Role::whereIn('name', ['Individual Student', 'Student'])->pluck('id'))->whereYear('created_at', '<', $year);
    //         });

    //         $graduate->update(['graduate_year' => $prevYear]);

    //         $student->update(['year_popup' => true]);
    //         $student->increment('standard_id', 1);

    //         DB::commit();
    //         Cache::tags(['profile'])->flush();
    //         $redirectUrl = url()->previous();
    // if($redirectUrl == url('updateUserSession') || strpos($redirectUrl,'api') !== false) {
    //     $redirectUrl = session()->get('previousUrl');
    // }
    // return redirect($redirectUrl)->with('message', 'Students year rolled over successfully!');
    //     } catch (\Exception $e) {
    //         DB::rollback();
    //         return $e->getMessage();
    //     }
    // }

    public function reSendInvitation($id)
    {
        $student = Student::find($id);
        dispatch((new ProcessStudentInvitation($student)));
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Automated welcome email with account details has been resent successfully.');
    }
}
