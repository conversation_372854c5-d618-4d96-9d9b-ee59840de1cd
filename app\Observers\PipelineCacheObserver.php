<?php

namespace App\Observers;

use App\Services\EmployerPipelineService;
use App\Employer;
use Illuminate\Support\Facades\Cache;

class PipelineCacheObserver
{
    protected $pipelineService;

    public function __construct(EmployerPipelineService $pipelineService)
    {
        $this->pipelineService = $pipelineService;
    }

    /**
     * Clear pipeline cache when workexperience responses are created/updated/deleted
     */
    public function handleWorkexperienceResponse($response)
    {
        $this->clearCacheForTemplate($response->template_id, 'workexperienceTemplates');
    }

    /**
     * Clear pipeline cache when skillstraining responses are created/updated/deleted
     */
    public function handleSkillstrainingResponse($response)
    {
        $this->clearCacheForTemplate($response->template_id, 'skillstrainingTemplates');
    }

    /**
     * Clear pipeline cache when lesson responses are created/updated/deleted
     */
    public function handleLessonResponse($response)
    {
        $this->clearCacheForLesson($response->lesson_id);
    }

    /**
     * Clear pipeline cache when gameplan industries are created/updated/deleted
     */
    public function handleGameplanIndustry($gameplanIndustry)
    {
        $this->clearCacheForIndustry($gameplanIndustry->industry_category_id);
    }

    /**
     * Clear pipeline cache when favorites are created/updated/deleted
     */
    public function handleFavorite($favorite)
    {
        if ($favorite->favoriteable_type === 'App\Industryunit') {
            $this->clearCacheForIndustryunit($favorite->favoriteable_id);
        }
    }

    /**
     * Clear pipeline cache when user follows are created/updated/deleted
     */
    public function handleUserFollow($userFollow)
    {
        // Clear cache for the employer who is following
        $employer = Employer::find($userFollow->follow_by);
        if ($employer) {
            $this->pipelineService->clearPipelineCache($employer);
        }
    }

    /**
     * Clear cache for employers whose companies have a specific template
     */
    private function clearCacheForTemplate($templateId, $relationName)
    {
        // Find companies that have this template
        $companyIds = \DB::table('company_' . str_replace('Templates', '_templates', $relationName))
            ->where(str_replace('Templates', '_template_id', $relationName), $templateId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for employers whose companies have a specific lesson
     */
    private function clearCacheForLesson($lessonId)
    {
        // Find companies that have this lesson
        $companyIds = \DB::table('company_lessons')
            ->where('lesson_id', $lessonId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for employers whose companies have a specific industry
     */
    private function clearCacheForIndustry($industryId)
    {
        // Find companies that have this industry
        $companyIds = \DB::table('company_industries')
            ->where('industry_category_id', $industryId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for employers whose companies have a specific industryunit
     */
    private function clearCacheForIndustryunit($industryunitId)
    {
        // Find companies that have this industryunit
        $companyIds = \DB::table('company_industryunits')
            ->where('industryunit_id', $industryunitId)
            ->pluck('company_id')
            ->toArray();

        $this->clearCacheForCompanies($companyIds);
    }

    /**
     * Clear cache for all employers of the given companies
     */
    private function clearCacheForCompanies(array $companyIds)
    {
        if (empty($companyIds)) {
            return;
        }

        // Find all employers for these companies
        $employers = Employer::whereIn('company_id', $companyIds)->get();

        foreach ($employers as $employer) {
            $this->pipelineService->clearPipelineCache($employer);
            
            // Also clear the student counts cache
            $pattern = "employer_student_counts_{$employer->id}_*";
            // Clear multiple possible cache keys (this is a simplified approach)
            for ($i = 0; $i < 100; $i++) {
                Cache::forget("employer_student_counts_{$employer->id}_" . $i);
            }
        }
    }

    /**
     * Clear all pipeline-related cache (use sparingly)
     */
    public function clearAllPipelineCache()
    {
        // This is a brute force approach - in production you might want to use Redis SCAN
        $employers = Employer::all();
        foreach ($employers as $employer) {
            $this->pipelineService->clearPipelineCache($employer);
        }
    }
}
