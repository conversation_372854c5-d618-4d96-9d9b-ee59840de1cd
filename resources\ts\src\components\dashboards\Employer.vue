<template>
    <div class="card card-flush mt-20 mt-lg-5 mb-6">
        <div class="card-body">
            <div class="d-flex gap-5 flex-wrap flex-sm-nowrap">
                <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
                    <img :src="companyData?.company_logo ? companyData.company_logo : ''" alt="company-logo">
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-center mb-2">
                                <a href="#" class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">
                                    {{ companyData?.company_name ? companyData.company_name : 'Loading...' }}
                                </a>
                                <a href="#"><i class="ki-outline ki-verify fs-1 text-primary"></i></a>
                            </div>
                            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                <div class="flex">
                                    <div class="fw-bold fs-6  text-gray-500 mb-1">Category</div>
                                    <a href="#" class="d-flex align-items-center fw-bold  t-gray-900 me-5 mb-2">
                                        <i class="ki-outline ki-profile-circle fs-4 me-1"></i>
                                        {{ companyData?.company_industry_categories ?
                                            companyData.company_industry_categories : 'Categories' }}
                                    </a>
                                </div>
                                <div class="flex">
                                    <div class=" fs-6 text-gray-500 mb-1">Location</div>
                                    <a href="#" class="d-flex align-items-center  fw-bold t-gray-900 me-5 mb-2">
                                        <i class="ki-outline ki-geolocation fs-4 me-1"></i>
                                        {{ companyData?.company_states ? companyData.company_states : 'State' }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap flex-stack">
                        <div class="d-flex flex-column flex-grow-1 pe-8">
                            <div class="d-flex flex-wrap">
                                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="fw-bold counted">{{ companyData?.company_created_at ?? '...' }}
                                        </div>
                                    </div>
                                    <div class="fw-semibold fs-6 text-gray-500">Active since</div>
                                </div>
                                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="fw-bold counted" data-kt-countup="true" :data-kt-countup-value="companyData?.company_modules_counts" data-kt-initialized="1">{{ companyData?.company_modules_counts ?? '0' }}
                                        </div>
                                    </div>
                                    <div class="fw-semibold fs-6 text-gray-500"> # of Linked Content

                                    </div>
                                </div>
                                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="fw-bold counted" data-kt-countup="true" :data-kt-countup-value="companyData?.pipeline_count" data-kt-initialized="1">{{ companyData?.pipeline_count ?? '0' }}</div>
                                    </div>
                                    <div class="fw-semibold fs-6 text-gray-500"># of students in Pipeline</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-xl-7">
            <div class="row row-cols-1 row-cols-sm-2 mb-5">
                <div class="col">
                    <div class="card card-flush h-100 mb-5">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Pipeline</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Overview</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="fs-3x">{{ companyData?.pipeline_count ?? '...' }}</p>
                            <p class="fs-2">Student{{ companyData?.pipeline_count > 1 ? 's' : '' }} in your pipeline</p>
                            <div class="d-flex justify-content-center mt-10">
                                <a href="/#/pipeline" target="_blank" class="btn btn-light d-flex align-items-center text-black gap-2 px-20">
                                    View Pipeline
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-flush h-100 mb-5">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Pipeline</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Insights</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="fs-2">Here's the top {{ dashboard?.topLocations?.length ?? '0' }} locations where
                                your Pipeline is based.</p>
                            <div class="row justify-content-center align-items-center mt-10">
                                <div class="col-5">
                                    <div id="kt_apex_pie_chart"></div>
                                </div>
                                <div class="col-7">
                                    <div v-for="(loc, i) in dashboard.topLocations" :key="loc.state_code" class="d-flex fs-6 fw-semibold align-items-center gap-3 my-1">
                                        <div class="bullet w-15px h-6px rounded-2" :style="{ backgroundColor: ['#0062FF', '#C7BB6A', '#FF571F'][i] }"></div>
                                        <div class="text-gray-500 flex-grow-1">
                                            {{ loc.state_name }} ({{ loc.student_count }})
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row row-cols-1 row-cols-sm-2 mb-5">
                <div class="col">
                    <div class="card card-flush h-100 mb-5">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Industries</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Latest Trends</span>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div id="kt_apex_curve_chart"></div>
                            <div class="d-flex flex-column gap-1">
                                <div v-for="industry in dashboard.industries" :key="industry" class="d-flex align-items-center gap-3 mb-1">
                                    <div style="width: 20px; height: 20px;" :style="{ backgroundColor: industryColors[industry] || '#E4E6EF' }"></div>
                                    <span>{{ industry }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-flush h-100 mb-5">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Modules</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Insights</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="fs-2">Your most engaged with modules.</p>
                            <div class="row justify-content-center align-items-center mt-10">
                                <div class="col-5">
                                    <div id="kt_apex_chart"></div>
                                </div>
                                <div class="col-7">
                                    <div v-for="(type, i) in dashboard.moduleTypes" :key="type" class="d-flex fs-6 fw-semibold align-items-center gap-3 w-100 my-1">
                                        <div class="bullet w-15px h-6px rounded-2" :style="{ backgroundColor: ['#0062FF', '#C7BB6A', '#FF571F'][i] }"></div>
                                        <div class="text-gray-500 flex-grow-1">
                                            {{ type }}
                                            <span v-if="dashboard.moduleTypeCounts && dashboard.moduleTypeCounts[type] !== undefined">
                                                ({{ dashboard.moduleTypeCounts[type] }})
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-xl-5 mb-5">
            <div class="card card-flush h-100">
                <div class="card-header pt-5">
                    <div class="card-title align-items-start flex-column">
                        <h2>Recent activity</h2>
                        <span class="text-gray-500 pt-1 fw-semibold fs-6">&nbsp;</span>
                    </div>
                </div>
                <div class="card-body pt-5">
                    <div class="timeline-label pb-10">
                        <template v-for="activity in timelineData" :key="activity.id">
                            <!-- <template v-for="activity in student.activities" :key="activity.id"> -->
                                <div class="timeline-item">
                                    <div class="timeline-label fw-bold text-gray-800">
                                        {{ activity.updated_at }}
                                    </div>
                                    <div class="timeline-badge">
                                        <i class="fa fa-genderless text-primary fs-1"></i>
                                    </div>
                                    <div class="timeline-content text-muted ps-3">
                                        <div v-html="activity.description"></div>
                                    </div>
                                </div>
                            <!-- </template> -->
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
    import { defineComponent, onMounted, ref, nextTick } from 'vue';
    import ApexCharts from 'apexcharts';
    import axios from 'axios';

    export default defineComponent({
        name: 'MainDashboard',
        setup() {
            const companyData = ref<any>(null);
            const dashboard = ref<any>({
                topLocations: [],
                moduleTypes: ['Skills Training', 'Lessons', 'Virtual Work Experience'],
                moduleTypeCounts: {},
                industries: []
            });
            const industryColors: Record<string, string> = {
                Nature: '#F1416C',
                Science: '#009EF7',
                'Environment and Agriculture': '#50CD89',
            };
            const timelineData = ref<any[]>([]);


            const fetchDashboardData = async () => {
                try {
                    // Fetch consolidated dashboard data (single request for everything)
                    const dashboardRes = await axios.get('/employer/get-dashboard-data');
                    const dashboardData = dashboardRes.data.data;
                    companyData.value = dashboardData;

                    // Update industries data
                    dashboard.value.industries = dashboardData.company_industry_categories
                        ? dashboardData.company_industry_categories.split(', ').filter(industry => industry.trim() !== '')
                        : [];

                    // Update modules data
                    dashboard.value.moduleTypeCounts = dashboardData.company_modules;
                    dashboard.value.moduleTypes = Object.keys(dashboardData.company_modules)
                        .sort((a, b) => dashboardData.company_modules[b] - dashboardData.company_modules[a]);
                    // Update top locations (now included in consolidated response)
                    dashboard.value.topLocations = dashboardData.top_locations || [];

                } catch (error) {
                    console.error('Error fetching dashboard data:', error);
                }
            };

            const renderCharts = () => {
                // Donut Chart for Modules
                const donutChartElement = document.querySelector("#kt_apex_chart");
                if (donutChartElement) {
                    const donutOptions = {
                        chart: {
                            type: 'donut',
                            height: 120,
                        },
                        series: dashboard.value.moduleTypes.map(type => dashboard.value.moduleTypeCounts[type] || 0),
                        labels: dashboard.value.moduleTypes,
                        colors: dashboard.value.moduleTypes.map((type, i) => ['#0062FF', '#C7BB6A', '#FF571F'][i]),
                        dataLabels: { enabled: false },
                        legend: { show: false },
                        stroke: { width: 0 },
                    };
                    new ApexCharts(donutChartElement, donutOptions).render();
                }
                // Pie Chart for Top Locations
                const pieChartElement = document.querySelector("#kt_apex_pie_chart");
                if (pieChartElement) {
                    const pieOptions = {
                        chart: {
                            type: 'pie',
                            height: 120,
                        },
                        series: dashboard.value.topLocations.map(loc => loc.student_count),
                        labels: dashboard.value.topLocations.map(loc => loc.state_name),
                        colors: ['#0062FF', '#C7BB6A', '#FF571F'],
                        dataLabels: { enabled: false },
                        legend: { show: false },
                        stroke: { width: 0 },
                    };
                    new ApexCharts(pieChartElement, pieOptions).render();
                }
                // Restore previous curve chart style
                const curveChartElement = document.querySelector("#kt_apex_curve_chart");
                if (curveChartElement) {
                    const curveOptions = {
                        chart: {
                            type: 'area', // Use 'area' for line with fill
                            height: 130,
                            toolbar: { show: false }
                        },
                        series: [{
                            name: 'Sales',
                            data: [40, 100, 40, 100, 40, 100, 40]
                        }],
                        stroke: {
                            curve: 'smooth',
                            width: 3,
                            colors: ['#0062FF'],
                        },
                        fill: {
                            type: 'solid',
                            colors: ['#0062FF'],
                            opacity: 0.15 // Adjust for shadow/softness
                        },
                        xaxis: {
                            labels: { show: false },
                            axisBorder: { show: false },
                            axisTicks: { show: false }
                        },
                        yaxis: {
                            labels: { show: false },
                            axisBorder: { show: false },
                            axisTicks: { show: false }
                        },
                        colors: ['#0062FF'],
                        dataLabels: { enabled: false },
                        // grid: { strokeDashArray: 4 },
                        grid: { show: false }
                    };
                    new ApexCharts(curveChartElement, curveOptions).render();
                }
            };

            const fetchPipelineActivities = async () => {
                try {
                    const res = await axios.get('/employer/pipeline-activities');
                    timelineData.value = res.data.timeline || [];
                } catch (error) {
                    console.error('Error fetching pipeline activities:', error);
                    timelineData.value = [];
                }
            };


            onMounted(async () => {
                await fetchDashboardData();
                await nextTick();
                renderCharts();
                await fetchPipelineActivities();
            });

            return {
                companyData,
                dashboard,
                industryColors,
                timelineData,
            };
        }
    });
</script>

<style scoped>
    .app-content {
        padding-top: 0px;
        padding-bottom: 0px;
    }

    /* Responsive ApexChart container */
    .responsive-apexchart {
        width: 100%;
        min-width: 0;
        overflow-x: auto;
    }

    .timeline-item .timeline-label {
        width: 160px;
        text-align: right;
        padding-right: 15px;
    }

    .timeline-label:before {
        left: 160px;
        top: 15px;
        height: 50px;
    }

    @media (max-width: 1400px) {
        #kt_card_widget_12_chart {
            height: 100px !important;
            min-height: 100px !important;
        }

        .responsive-apexchart svg {
            height: 100px !important;
        }
    }

    @media (max-width: 991px) {
        #kt_card_widget_12_chart {
            height: 80px !important;
            min-height: 80px !important;
        }

        .responsive-apexchart svg {
            height: 80px !important;
        }
    }

    @media (max-width: 575px) {
        #kt_card_widget_12_chart {
            height: 60px !important;
            min-height: 60px !important;
        }

        .responsive-apexchart svg {
            height: 60px !important;
        }
    }
</style>