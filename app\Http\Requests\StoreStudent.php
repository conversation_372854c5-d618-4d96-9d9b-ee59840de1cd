<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreStudent extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        // return $this->user()->role_id == 1;
        return in_array($this->user()->role_id, [1,2]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if($this->user()->isAdmin()) {
            return [
                'firstname' => 'required|max:255',
                'lastname' => 'required|max:255',
                'email' => [
                    'required',
                    'email:dns',
                    'unique:users,email',
                    function ($attribute, $value, $fail) {
                        $domain = substr(strrchr($value, "@"), 1);
                        if (!checkdnsrr($domain, 'MX')) {
                            $fail('The email domain does not have valid MX records.');
                        }
                    },
                ],
                //'password' => 'required',
                //'gender' => 'required',
                'school' => 'required_without:organisation',
                'organisation' => 'required_without:school',
                'other_gender' => 'required_if:gender,Other',
                // 'state' => 'required',
                // 'postcode' => 'required',
                // 'year' => 'required',
                //'finishingyear' => 'required'
            ];
        } elseif($this->user()->isTeacher()) {
            return [
                'firstname' => 'required|max:255',
                'lastname' => 'required|max:255',
                'email' => [
                    'required',
                    'email:dns',
                    'unique:users,email',
                    function ($attribute, $value, $fail) {
                        $domain = substr(strrchr($value, "@"), 1);
                        if (!checkdnsrr($domain, 'MX')) {
                            $fail('The email domain does not have valid MX records.');
                        }
                    },
                ],
                // 'password' => 'required',
                //'gender' => 'required',
                // 'state' => 'required',
                // 'postcode' => 'required',
                // 'year' => 'required',
                //'finishingyear' => 'required'
            ];
        }
    }

    public function messages()
    {
        return [
            'email.unique' => "This email is already in use!",
            'other_gender.required_if' => "This filed is required."
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */

}
