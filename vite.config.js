const { defineConfig, loadEnv } = require("vite");

module.exports = defineConfig(async ({ mode }) => {
  const { default: laravel } = await import("laravel-vite-plugin");
  const { default: vue } = await import("@vitejs/plugin-vue");
  const { default: react } = await import("@vitejs/plugin-react");
  const { resolve } = require("path");
  const env = loadEnv(mode, process.cwd());

  return {
    plugins: [
      laravel({
        input: [
          "resources/css/app.css", 
          "resources/js/app.js", 
          "resources/ts/vapp.ts", 
          "resources/react/rapp.tsx", 
          "resources/css/custom.css"
        ],
        refresh: true,
      }),
      vue({
        template: {
          transformAssetUrls: {
            base: null,
            includeAbsolute: false,
          },
        },
      }),
      react(),
    ],
    resolve: {
      alias: {
        // For Vue files, @ should point to Vue src
        "@": resolve(__dirname, "resources/ts/src"),
        // For React files, use explicit @react
        "@react": resolve(__dirname, "resources/react/src"),
        vue: "vue/dist/vue.esm-bundler.js",
      },
    },
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
    },
    build: {
      rollupOptions: {
        output: {
          entryFileNames: 'assets/[name].js',
          chunkFileNames: 'assets/[name].js',
          assetFileNames: 'assets/[name][extname]',
          manualChunks: {
            vendor: ["vue", "react", "react-dom"],
            jquery: ["jquery"],
          },
        },
      },
    },
    server: {
      hmr: {
        host: env.VITE_HMR_HOST || "localhost",
        port: parseInt(env.VITE_HMR_PORT) || 5174,
        protocol: env.VITE_HMR_PROTOCOL || "ws",
      },
      host: env.VITE_DEV_SERVER_HOST || "0.0.0.0",
      port: parseInt(env.VITE_DEV_SERVER_PORT) || 5174,
      cors: {
        origin: [
          env.VITE_APP_URL,
          env.VITE_APP_URL?.replace("https:", "http:"),
          "http://localhost:5174",
          "http://localhost:5175",
          "http://127.0.0.1:5174",
          "http://127.0.0.1:5175",
          "http://127.0.0.1:8000",
          "https://the-careers-department.test",
          "http://the-careers-department.test",
          "https://localhost:5174",
          "https://localhost:5175",
        ].filter(Boolean),
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin", "X-CSRF-TOKEN"],
        exposedHeaders: ["Content-Length", "X-JSON"],
      },
      https: env.VITE_DEV_SERVER_HTTPS === "true",
      strictPort: false,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN",
        "Access-Control-Allow-Credentials": "true",
      },
      proxy: {
        '/fonts/': {
          target: env.VITE_APP_URL,
          changeOrigin: true,
        },
      },
    },
  };
});
