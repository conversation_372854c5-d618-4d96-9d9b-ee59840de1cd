<template>
    <div class="d-flex flex-column flex-lg-row flex-column-fluid stepper stepper-pills stepper-column stepper-multistep first"
        ref="wizardRef" id="kt_create_account_stepper">
        <div class="d-none flex-column flex-lg-row-auto w-lg-350px w-xl-500px">
            <div class="top-0 bottom-0 d-flex flex-column position-lg-fixed w-lg-350px w-xl-500px scroll-y bgi-size-cover bgi-position-center"
                style="background-image: url(media/misc/auth-bg.png)">
                <div class="py-10 d-flex flex-center py-lg-20 mt-lg-20">
                    <router-link to="/">
                        <img alt="Logo" src="media/logos/custom-1.png" class="h-70px" />
                    </router-link>
                </div>
                <div class="p-10 d-flex flex-row-fluid justify-content-center">
                    <div class="stepper-nav">
                        <div class="stepper-item " :class="[(currentStepIndex == 0) ? 'current' : 'pending']"
                            data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon rounded-3">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">1</span>
                                </div>
                                <div class="stepper-label">
                                    <p class="stepper-title fs-2x fw-bold">Account Type</p>
                                    <div class="stepper-desc fw-normal">
                                        Select your account type
                                    </div>
                                </div>
                            </div>
                            <div class="stepper-line h-40px"></div>
                        </div>
                        <div class="stepper-item" :class="[(currentStepIndex == 1) ? 'current' : 'pending']"
                            data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon rounded-3">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">2</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title fs-2">Account Settings</h3>
                                    <div class="stepper-desc fw-normal">
                                        Setup your account settings
                                    </div>
                                </div>
                            </div>
                            <div class="stepper-line h-40px"></div>
                        </div>
                        <div class="stepper-item" :class="[(currentStepIndex == 2) ? 'current' : 'pending']"
                            data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">3</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title fs-2">Business Details</h3>
                                    <div class="stepper-desc fw-normal">
                                        Setup your business details
                                    </div>
                                </div>
                            </div>
                            <div class="stepper-line h-40px"></div>
                        </div>
                        <div class="stepper-item " :class="[(currentStepIndex == 3) ? 'current' : 'pending']"
                            data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">4</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title">Next</h3>
                                    <div class="stepper-desc fw-normal">
                                        Enter School Password
                                    </div>
                                </div>
                            </div>
                            <div class="stepper-line h-40px"></div>
                        </div>
                        <div class="stepper-item " :class="[(currentStepIndex == 4) ? 'current' : 'pending']"
                            data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">4</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title">Next</h3>
                                    <div class="stepper-desc fw-normal">
                                        Enter School Password
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stepper-item " :class="[(currentStepIndex == 5) ? 'current' : 'pending']"
                            data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">5</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title">Next</h3>
                                    <div class="stepper-desc fw-normal">
                                        Enter School Password
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-wrap px-5 py-10 d-flex flex-center">
                    <div class="d-flex fw-normal">
                        <a href="" class="px-5 text-success" target="_blank">Terms</a>
                        <a href="" class="px-5 text-success" target="_blank">Plans</a>
                        <a href="" class="px-5 text-success" target="_blank">Contact Us</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-column flex-lg-row-fluid">
            <form class="pb-5 text-gray-700" novalidate="novalidate" id="kt_create_account_form" @submit="handleStep">
                <div class="" :class="[(currentStepIndex == 0) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step1 />
                </div>

                <div class="" :class="[(currentStepIndex == 1) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step2 @schoolNotFound="setSchoolUnavailable" :formData="formData" />
                </div>

                <div class="" :class="[(currentStepIndex == 2) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step3 :formData="formData" />
                </div>

                <div class="" :class="[(currentStepIndex == 3) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step4 :formData="formData" />
                </div>

                <div class="" :class="[(currentStepIndex == 4) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step5 :formData="formData" />
                </div>
                <div class="" :class="[(currentStepIndex == 5) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step6 :formData="formData" />
                </div>
                <div class="pt-10">
                    <!-- <button type="button" class="btn btn-lg btn-light-primary me-3 w-100 rounded-0 mb-10 d-none" data-kt-stepper-action="previous" @click="previousStep">
                        <span class="svg-icon svg-icon-4 me-1">
                      <inline-svg src="media/icons/duotune/arrows/arr063.svg" />
                    </span>
                        Back
                    </button> -->
                    <button type="submit" class="mb-5 btn btn-lg btn-primary w-100 rounded-0"
                        data-kt-stepper-action="submit" v-if="currentStepIndex === totalSteps - 1" @click="processing">
                        <span class="indicator-label"> INVITE </span>
                        <span class="indicator-progress"> Please wait... <span
                                class="align-middle spinner-border spinner-border-sm ms-2"></span> </span>
                    </button>
                    <button type="submit" class="btn btn-lg btn-primary w-100 rounded-0" @click="skipparentinvite"
                        v-if="currentStepIndex === totalSteps - 1">
                        <span class="indicator-label"> SKIP </span>
                        <span class="indicator-progress"> Please wait... <span
                                class="align-middle spinner-border spinner-border-sm ms-2"></span> </span>
                    </button>
                    <button v-else type="submit" class="btn btn-lg btn-primary w-100 rounded-0">
                        NEXT

                    </button>
                    <!-- <span @click="gotodetails()">Go to details</span> -->
                </div>
            </form>
        </div>
    </div>
</template>
<script lang="ts">
import ApiService from "@/core/services/ApiService";
import { computed, defineComponent, onMounted, ref } from "vue";
import LayoutService from "@/core/services/LayoutService";
import { useRegisterStore } from "@/stores/Auth/RegisterStore";
import { getIllustrationsPath } from "@/core/helpers/assets";
import Step1 from "@/views/crafted/authentication/student/steps/Step1.vue";
import Step2 from "@/views/crafted/authentication/student/steps/Step2.vue";
import Step3 from "@/views/crafted/authentication/student/steps/Step3.vue";
import Step4 from "@/views/crafted/authentication/student/steps/Step4.vue";
import Step5 from "@/views/crafted/authentication/student/steps/Step5.vue";
import Step6 from "@/views/crafted/authentication/student/steps/Step6.vue";
import { StepperComponent, defaultStepperOptions } from "@/assets/ts/components";
import * as Yup from "yup";
import { useForm, configure } from "vee-validate";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { Actions, Mutations } from "@/store/enums/StoreEnums";
import axios from "axios";
import { storeToRefs } from "pinia";
import { debounce } from "lodash";

configure({
    validateOnBlur: false,
    validateOnChange: false,
    validateOnInput: false,
    validateOnModelUpdate: false
});
interface IStep1 {
    inSchool: string;
}

interface IStep2 {
    schoolName: string;
    schoolUnavailable: boolean
}

interface IStep3 {
    schoolPassword: string;
    schoolCampus: string;
}

interface IStep4 {
    // licens: string;
}

interface IStep5 {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    password_confirmation: string;
    state: string;
    postcode: string;
    gender: string;
    genderOther: string;
    year: string;
    gradYear: string;
}
interface IStep6 {
    parentInvite: boolean
    parentfname: any
    parentemail: any
    parentlname: any
}
interface CreateAccount extends IStep1, IStep2, IStep3, IStep4, IStep5, IStep6 { }

export default defineComponent({
    name: "multistepsignup",
    emits: ['schoolNotFound'],

    components: {
        Step1,
        Step2,
        Step3,
        Step4,
        Step5,
        Step6
    },
    setup() {
        const store = useStore();
        const router = useRouter();
        const authStore = useRegisterStore();
        const filteredFirstUser = storeToRefs(authStore);

        const _stepperObj = ref<StepperComponent | null>(null);
        // defaultStepperOptions.startIndex=authStore.currentStage;
        const wizardRef = ref<HTMLElement | null>(null);
        const currentStepIndex = ref(0);
        const formData = ref<CreateAccount>({
            parentInvite: true,
            inSchool: authStore.studentDetail.inSchool,
            schoolUnavailable: false,
            schoolName: authStore.studentDetail.schoolName,
            schoolPassword: authStore.studentDetail.schoolPassword,
            schoolCampus: authStore.studentDetail.schoolCampus,
            firstName: authStore.studentDetail.firstName,
            lastName: authStore.studentDetail.lastName,
            email: authStore.email,
            password: '',
            password_confirmation: '',
            state: authStore.studentDetail.state,
            postcode: authStore.studentDetail.postcode,
            gender: authStore.studentDetail.gender,
            genderOther: authStore.studentDetail.genderOther,
            year: authStore.studentDetail.year,
            gradYear: authStore.studentDetail.gradYear,
            parentemail: '',
            parentfname: '',
            parentlname: ''

        });

        onMounted(() => {

            _stepperObj.value = StepperComponent.createInsance(
                wizardRef.value as HTMLElement, defaultStepperOptions
            );

            LayoutService.emptyElementClassesAndAttributes(document.body);

            store.dispatch(Actions.ADD_BODY_CLASSNAME, "app-blank");

            store.dispatch(Actions.ADD_BODY_CLASSNAME, "bg-body");
        });
        const testSchoolsPassword = async (value) => {
            if (currentStepIndex.value == 2 && value.length) {
                var f = { ...formData.value };
                await store.dispatch(Actions.VALIDATE_SCHOOL_PASS, { 'schoolPass': value, schoolid: f.schoolName });
                var [errorName] = Object.keys(store.getters.getErrors);
                var error = store.getters.getErrors[errorName];

                if (!error) {
                    // console.log("Success returned ", error);
                    return true;
                }
                return false;
            } else {
                return true;
            }
        };

        const testSchoolsCampus = async (value) => {
            // console.log("value",value,authStore.studentDetail);
            if (!value.length && authStore.studentDetail.schoolCampuses.length) {
                return false;
            } else {
                return true;
            }
        };
        const testYearRequired = async (value) => {
            if (authStore.studentDetail.inSchool == "notinschool") {
                formData.value.year = "7";
                return true;
            }
            if (authStore.studentDetail.inSchool == "inschool" && !value.length) {
                // console.log("Test Year notinschool");
                return false;
            } else {
                return true;
            }
        };

        const testGradYearRequired = async (value) => {
            if (authStore.studentDetail.inSchool == "notinschool" && !value.length) {
                // console.log("Test Year notinschool");
                return false;
            } else {
                return true;
            }
        };

        const setSchoolUnavailable = function (value) {
            authStore.studentDetail.schoolUnavailable = formData.value.schoolUnavailable = value;
            if (value) {
                // if (!_stepperObj.value) {
                //   return;
                // }
                // _stepperObj.value.totatStepsNumber--;
            }
        }

         const validateEmailDomain = debounce(async (value) => {
            try {
                // const activeElement = document.activeElement as HTMLInputElement | null; || (activeElement?.name !== 'email')
                if (!value || value.trim() === '') {
                    return true;
                }
                const response = await axios.post('/register/validate-email', { email: value });
                return response.data.valid;
            } catch (error) {
                return false;
            }
        }, 500);

        const createAccountSchema = [
            Yup.object({
                inSchool: Yup.string().nullable().required().label("Stage Of Life"),
            }),

            Yup.object({
                schoolName: Yup.string().required().label("School Name"),
            }),
            Yup.object({
                schoolPassword: Yup.string().required().label("School code")
                    .test('School-Password', 'Wrong school code', testSchoolsPassword),
                // .test('School-Limit', '', testSchoolLimit),
                schoolCampus: Yup.string().nullable().label('Campus')
                    .test('School-Password', 'Please select your campus', testSchoolsCampus),
            }),
            Yup.object({

            }),
            Yup.object({
                email: Yup.string()
                    .email('Must be a valid email')
                    .required('Email is required')
                    .test('email-domain', 'Email domain is not valid', async function (value) {
                        if (!value || value.trim() === '' || currentStepIndex.value !== 4) {
                            return true;
                        }
                        return await validateEmailDomain(value);
                    }),
                firstName: Yup.string().required('First Name is required'),
                lastName: Yup.string().required('Last Name is required'),
                password: Yup.string().required('Password is required')
                .min(8, 'Password must be at least 8 characters')
                .matches(/[0-9!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one number or special character'),
                state: Yup.string().required('State is required'),
                postcode: Yup.string().required('Postcode is required'),
                gender: Yup.string().required('Please select your gender'),
                year: Yup.string().test("Year", "Year is required", testYearRequired),
                // gradYear: Yup.string().test("gradYear", "Year is required", testGradYearRequired),
                password_confirmation: Yup.string()
                    .oneOf([Yup.ref('password'), null], 'Passwords must match')
            }),
            Yup.object({
                parentInvite: Yup.boolean(),
                parentemail: Yup.string().when('parentInvite', {
                    is: (val) => { return formData.value.parentInvite == true },
                    then: Yup.string().email().required('Email is required')
                }),
                parentfname: Yup.string().when('parentInvite', {
                    is: (val) => { return formData.value.parentInvite == true },
                    then: Yup.string().required('First Name is required')
                }),
                parentlname: Yup.string().when('parentInvite', {
                    is: (val) => { return formData.value.parentInvite == true },
                    then: Yup.string().required('Last Name is required')
                }),
            })
        ];

        const currentSchema = computed(() => {
            return createAccountSchema[currentStepIndex.value];
        });
        // const currentStepIs = ((step) => {
        //   console.log(step);
        //   return currentStepIndex.value==step
        // });

        const { resetForm, handleSubmit } = useForm<
            IStep1 | IStep2 | IStep3 | IStep4 | IStep5 | IStep6
        >({
            validationSchema: currentSchema,
        });

        const totalSteps = computed(() => {
            if (!_stepperObj.value) {
                return;
            }

            return _stepperObj.value.totatStepsNumber;
        });
        const submitFormManually = () => {
            // console.log("called", formData.value.firstName);
        }
        resetForm({
            values: {
                ...formData.value,
            },
        });

        const handleStep = handleSubmit(async (values) => {
            resetForm({
                values: {
                    ...formData.value,
                },
            });
            formData.value = {
                ...formData.value,
                ...values,
            };

            if (currentStepIndex.value == 0) {
                authStore.studentDetail.inSchool = formData.value.inSchool;

                if (formData.value.inSchool == 'notinschool') {
                    authStore.studentDetail.year = "7";
                    if (!_stepperObj.value) {
                        return;
                    }
                    currentStepIndex.value = 3;
                    _stepperObj.value.goto(4);
                    return;
                }
            } else if (currentStepIndex.value == 1) {
                // currentStepIndex.value = 3;
                // _stepperObj.value.goNext();
                if (authStore.studentDetail.schoolUnavailable) {
                    if (!_stepperObj.value) {
                        return;
                    }
                    // console.log(currentStepIndex.value + 'gg');
                    currentStepIndex.value = 3;
                    _stepperObj.value.goto(4);
                    return;
                }
            } else if (currentStepIndex.value == 2) {
                if (formData.value.inSchool == 'inschool') {
                    // currentStepIndex.value++;
                    // authStore.currentStage=currentStepIndex.value;
                    if (!_stepperObj.value) {
                        return;
                    }
                    await store.dispatch(Actions.CHECK_SCHOOL_LIMIT, { schoolid: authStore.studentDetail.school.id });
                    var [errorName] = Object.keys(store.getters.getErrors);
                    var error = store.getters.getErrors[errorName];
                    if (error) {
                        Swal.fire({
                            text: error.message,
                            icon: "warning",
                            buttonsStyling: false,
                            confirmButtonText: "OK",
                            customClass: {
                                confirmButton: "btn fw-semobold btn-light-primary",
                            },
                        }).then(() => {
                            router.push({ name: "sign-in" });
                            // window.location.reload();
                        });
                        return;
                    }

                    currentStepIndex.value = 4;
                    _stepperObj.value.goto(5);
                    return;
                }
            } else if (currentStepIndex.value == 3) {


            } else if (currentStepIndex.value == 4) {
                authStore.studentDetail.firstName = formData.value.firstName;
                authStore.studentDetail.lastName = formData.value.lastName;
                authStore.email = formData.value.email;
                authStore.studentDetail.password = formData.value.password;
                authStore.studentDetail.password_confirmation = formData.value.password_confirmation;
                authStore.studentDetail.state = formData.value.state;
                authStore.studentDetail.postcode = formData.value.postcode;
                authStore.studentDetail.gender = formData.value.gender;
                authStore.studentDetail.genderOther = formData.value.genderOther;
                authStore.studentDetail.year = formData.value.year;
                authStore.studentDetail.gradYear = formData.value.gradYear;
                authStore.studentDetail.schoolPassword = formData.value.schoolPassword;
                authStore.studentDetail.schoolCampus = formData.value.schoolCampus;

                // If user is not in school, skip parent invite and submit registration
                if (formData.value.inSchool === 'notinschool') {
                    await submitRegistrationForm(values);
                    return;
                }
            } else if (currentStepIndex.value == 5) {
                // console.log("formData", formData.value);
                authStore.studentDetail.parent.firstname = formData.value.parentfname;
                authStore.studentDetail.parent.lastname = formData.value.parentlname;
                authStore.studentDetail.parent.email = formData.value.parentemail;
                submitRegistrationForm(values);
                return;
            }
            currentStepIndex.value++;
            authStore.currentStage = currentStepIndex.value;
            // console.log(_stepperObj.value);
            /* Check if last step , if so submit form  */
            if (!_stepperObj.value) {
                return;
            }

            _stepperObj.value.goNext();
        });
        const submitRegistrationForm = async (values) => {
            const submitButtons = document.querySelectorAll('button[type="submit"]');

            // Explicitly cast each element to HTMLButtonElement
            const submitButtonsArray: HTMLButtonElement[] = Array.from(submitButtons) as HTMLButtonElement[];

            // Disable all submit buttons
            submitButtonsArray.forEach(button => {
                button.disabled = true;
            });


            var postdata = {
                "accountType": authStore.accountType,
                "currentStage": authStore.currentStage,
                "email": authStore.email,
                "isNew": authStore.isNew,
                "parentDetail": authStore.parentDetail,
                "studentDetail": authStore.studentDetail,
                "teacherDetail": authStore.teacherDetail,
            }

            if (authStore.studentDetail.schoolUnavailable || authStore.studentDetail.inSchool == 'notinschool') {
                const payload = {
                    email: authStore.studentDetail.email,
                    studentDetail: {
                        firstName: authStore.studentDetail.firstName,
                        lastName: authStore.studentDetail.lastName,
                        password: authStore.studentDetail.password,
                        state: authStore.studentDetail.state,
                        postcode: authStore.studentDetail.postcode,
                        inSchool: authStore.studentDetail.inSchool,
                        school: authStore.studentDetail.school
                            ? { id: authStore.studentDetail.school.id }
                            : null,
                        year: authStore.studentDetail.year,
                        gradYear: authStore.studentDetail.gradYear,
                        gender: authStore.studentDetail.gender,
                        genderOther: authStore.studentDetail.genderOther,
                        parent: {
                            email: authStore.studentDetail.parent?.email,
                        },
                    },
                };
                await store.dispatch(Actions.BUY_INDIVIDUAL_LICENSE, payload);
            } else {
                await store.dispatch(Actions.REGISTER, postdata);
            }


            const [errorName] = Object.keys(store.getters.getErrors);
            const error = store.getters.getErrors[errorName];

            if (!error) {
                if (authStore.studentDetail.schoolUnavailable || authStore.studentDetail.inSchool == 'notinschool') {
                    const stripedata = store.getters.getStripeData;
                    if (stripedata['url']?.length) {
                        window.location.href = stripedata['url'];
                    }

                } else {
                    router.push({ name: "dashboard" });
                }
            } else {
                var errormsg = '';
                if (typeof error.password != 'undefined') {
                    errormsg = error.password
                } else if (error instanceof Array) {
                    errormsg = error[0] ?? '';
                }
                Swal.fire({
                    text: errormsg,
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Try again!",
                    customClass: {
                        confirmButton: "btn fw-semobold btn-light-danger",
                    },
                });
            }
            return true;
        };

        const processing = (e) => {
            e.target.setAttribute("data-kt-indicator", "on");
        };
        const skipparentinvite = (e) => {
            // skipparentinvite

            formData.value.parentInvite = false;
            var f = { ...formData.value };
            f.parentInvite = false;
            e.target.setAttribute("data-kt-indicator", "on");
        };

        const previousStep = () => {
            if (!_stepperObj.value) {
                return;
            }

            currentStepIndex.value--;

            _stepperObj.value.goPrev();
        };
        const gotodetails = () => {
            if (!_stepperObj.value) {
                return;
            }
            currentStepIndex.value = 4;
            _stepperObj.value.goto(5);
        };

        const formSubmit = () => {

            Swal.fire({
                text: "All is cool! Now you submit this form",
                icon: "success",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn fw-semobold btn-light-primary",
                },
            }).then(() => {
                // window.location.reload();
            });
        };

        return {
            wizardRef,
            previousStep,
            processing,
            handleStep,
            formSubmit,
            totalSteps,
            currentStepIndex,
            getIllustrationsPath,
            formData,
            handleSubmit,
            submitFormManually,
            setSchoolUnavailable,
            gotodetails,
            skipparentinvite
        };
    },
});
</script>
<style>
.multiselect-clear-icon {
    display: none !important;
}

.swal2-popup {
    border-radius: 0px;
}
</style>
