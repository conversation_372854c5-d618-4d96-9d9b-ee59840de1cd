<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserFollow extends Model
{
    protected $fillable = ['follow_by', 'following'];

    protected static function booted()
    {
        // Clear pipeline cache when user follows change
        $clearPipelineCache = function ($userFollow) {
            app(\App\Services\EmployerPipelineService::class)->clearCacheForUserFollow($userFollow);
        };
        
        static::created($clearPipelineCache);
        static::updated($clearPipelineCache);
        static::deleted($clearPipelineCache);
    }

    // Relationships
    public function follower()
    {
        return $this->belongsTo(User::class, 'follow_by');
    }

    public function following()
    {
        return $this->belongsTo(User::class, 'following');
    }
}
