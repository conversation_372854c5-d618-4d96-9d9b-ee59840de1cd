<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * These indexes are specifically designed to optimize the employer pipeline queries
     * that were causing performance issues in the pipelineStudents() method.
     *
     * @return void
     */
    public function up(): void
    {
        // Optimize workexperience_responses queries
        Schema::table('workexperience_responses', function (Blueprint $table) {
            // For: whereIn('template_id', $ids)->where('status', 'Submitted')
            $table->index(['template_id', 'status', 'student_id'], 'idx_we_responses_template_status_student');
            // For: where('status', 'Submitted') queries
            $table->index(['status', 'student_id'], 'idx_we_responses_status_student');
        });

        // Optimize skillstraining_responses queries
        Schema::table('skillstraining_responses', function (Blueprint $table) {
            // For: whereIn('template_id', $ids)->where('status', 'Submitted')
            $table->index(['template_id', 'status', 'student_id'], 'idx_st_responses_template_status_student');
            // For: where('status', 'Submitted') queries
            $table->index(['status', 'student_id'], 'idx_st_responses_status_student');
        });

        // Optimize lessonresponses queries
        Schema::table('lessonresponses', function (Blueprint $table) {
            // For: whereIn('lesson_id', $ids)->where('status', 'Submitted')
            $table->index(['lesson_id', 'status', 'student_id'], 'idx_lesson_responses_lesson_status_student');
            // For: where('status', 'Submitted') queries
            $table->index(['status', 'student_id'], 'idx_lesson_responses_status_student');
        });

        // Optimize gameplan_industries queries
        Schema::table('gameplan_industries', function (Blueprint $table) {
            // For: whereIn('industry_category_id', $ids) with gameplan joins
            $table->index(['industry_category_id', 'gameplan_id'], 'idx_gameplan_industries_category_gameplan');
        });

        // Optimize gameplans queries
        Schema::table('gameplans', function (Blueprint $table) {
            // For: MAX(id) subqueries grouped by user_id
            $table->index(['user_id', 'id'], 'idx_gameplans_user_id');
            // For: user_id lookups
            $table->index('user_id', 'idx_gameplans_user');
        });

        // Optimize favorites queries
        Schema::table('favorites', function (Blueprint $table) {
            // For: where('favoriteable_type', 'App\Industryunit')->whereIn('favoriteable_id', $ids)
            $table->index(['favoriteable_type', 'favoriteable_id', 'user_id'], 'idx_favorites_type_id_user');
            // For: where('favoriteable_type', 'App\Industryunit') queries
            $table->index(['favoriteable_type', 'user_id'], 'idx_favorites_type_user');
        });

        // Optimize user_follows queries (for followed students)
        Schema::table('user_follows', function (Blueprint $table) {
            // For: where('follow_by', $employer_id) queries
            $table->index(['follow_by', 'following'], 'idx_user_follows_by_following');
        });

        // Optimize users table for state-based filtering
        Schema::table('users', function (Blueprint $table) {
            // For: join with states and role-based filtering
            $table->index(['state_id', 'role_id'], 'idx_users_state_role');
        });

        // Add indexes for company relationships if they don't exist
        if (Schema::hasColumn('users', 'company_id')) {
            // This index might already exist, but ensure it's there for employer queries
            Schema::table('users', function (Blueprint $table) {
                $table->index(['company_id', 'role_id'], 'idx_users_company_role');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('workexperience_responses', function (Blueprint $table) {
            $table->dropIndex('idx_we_responses_template_status_student');
            $table->dropIndex('idx_we_responses_status_student');
        });

        Schema::table('skillstraining_responses', function (Blueprint $table) {
            $table->dropIndex('idx_st_responses_template_status_student');
            $table->dropIndex('idx_st_responses_status_student');
        });

        Schema::table('lessonresponses', function (Blueprint $table) {
            $table->dropIndex('idx_lesson_responses_lesson_status_student');
            $table->dropIndex('idx_lesson_responses_status_student');
        });

        Schema::table('gameplan_industries', function (Blueprint $table) {
            $table->dropIndex('idx_gameplan_industries_category_gameplan');
        });

        Schema::table('gameplans', function (Blueprint $table) {
            $table->dropIndex('idx_gameplans_user_id');
            $table->dropIndex('idx_gameplans_user');
        });

        Schema::table('favorites', function (Blueprint $table) {
            $table->dropIndex('idx_favorites_type_id_user');
            $table->dropIndex('idx_favorites_type_user');
        });

        Schema::table('user_follows', function (Blueprint $table) {
            $table->dropIndex('idx_user_follows_by_following');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_state_role');
            if (Schema::hasColumn('users', 'company_id')) {
                $table->dropIndex('idx_users_company_role');
            }
        });
    }
};
