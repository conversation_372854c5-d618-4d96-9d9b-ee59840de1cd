<?php

namespace App\Exports;

use App\User;
use App\Standard;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Carbon\Carbon;

class StudentsExport implements FromView,WithChunkReading,ShouldQueue
{
    use Exportable;

    public $user, $schoolSection, $request, $fullname, $year, $gender, $campus, $status, $profiler, $registration, $email, $graduate_year;

    public function __construct($request, $user, $schoolSection)
    {
        $this->user = $user;
        $this->schoolSection = $schoolSection;
        $this->request = $request;
        $this->fullname = $request['fullname'];
        $this->year = $request['year'];
        $this->gender = $request['gender'];
        $this->campus = $request['campus'];
        $this->status = $request['status'];
        $this->profiler = $request['profiler'];
        $this->registration = $request['registration'];
        $this->email = $request['email'];
        $this->graduate_year = $request['graduate_year'];
        $this->status = $request['status'];
        $this->school = $this->user->isTeacher()? $this->user->school_id : $request['schoolname'];
        if($this->user->isAdmin()){
           $this->created_in = $request['created_in'];
           $this->orgname = $request['orgname'];
           $this->orgCampus = $request['orgCampus'];
        }
    }

    public function view(): View
    {
        $gender = $this->gender;
        $registrationstatus =  $this->registration;

        $students = User::select('users.id', 'users.name', 'users.email', 'users.role_id', 'users.school_id', 'users.organisation_id', 'users.created_at', 'profiles.user_id', 'profiles.standard_id', 'profiles.graduate_year', 'profiles.school as profile_school', 'profiles.accountcreated', 'profiles.gender as gender', 'profiles.firstname as firstname', 'profiles.lastname as lastname', 'standards.title as year_title')
        ->join('profiles', 'users.id', '=', 'profiles.user_id')
        ->leftJoin('standards', 'profiles.standard_id', '=', 'standards.id')
        ->whereIn('users.role_id', [3, 4])
        ->with('parents', 'sessionStats', 'campuses', 'parents', 'parents:name', 'school.detail:school_id,subscription_ending_on,order_confirmed,confirmed_at')

        ->when($this->user->isTeacher() , function ($query){
            $campus = false;
            $campuses = $this->user->campuses()->pluck('campus_id');

            if ($campuses->count()) {
                if ($this->campus) {
                    $campus = array($this->campus);
                } else {
                    $campus = $campuses;
                }
            }
            ($query->where('users.school_id', $this->user->school_id))
                ->when($campus, function ($query) use ($campus) {
                    $query->where(function ($query) use ($campus) {
                        if ($this->campus != null) {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            });
                        } else {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            })->orDoesntHave('campuses');
                        }
                    });
                })
                ->when($this->schoolSection == 'primary', function ($query) {
                    return $query->whereHas("profile", function ($query) {
                        $years = Standard::primarySchool()->pluck('id');
                        $query->whereIn('standard_id', $years);
                    });
                })
                ->when($this->schoolSection == 'secondary', function ($query) {
                    return $query->whereHas("profile", function ($query) {
                        $years = Standard::secondarySchool()->pluck('id');
                        $query->whereIn('standard_id', $years)->orWhere(function ($query) {
                            $query->where('standard_id', null)
                                ->where('accountcreated', 0);
                        });
                    });
                });
        })

        ->when($this->fullname, function ($query) {
            $query->where('name', 'like', "%{$this->fullname}%");
        })
        ->when($this->email, function ($query) {
            $query->where('email', 'like', "%{$this->email}%");
        })

        ->when($this->school , function ($query){
            $query->where('school_id', $this->school);
        })

        ->when($this->school  && $this->campus , function ($query) {
            $query->whereHas('campuses', function ($q) {
                $q->where('campus_id', $this->campus);
            });
        })
        ->when($gender, function ($query) use ($gender)  {
            if($gender == 'M' || $gender =='F'){
                $query->whereHas('profile', function ($q) use ($gender) {
                  $q->whereGender($gender);
                });
            }else{
                $query->whereHas('profile', function ($q) use ($gender) {
                  $q->whereNotIn('gender', ['M', 'F']);
                });
            }
        })
        ->when($this->profiler, function ($query) {
            if ($this->profiler == 'completed') {
                $query->whereHas('profilerResult');
            } elseif ($this->profiler == 'incomplete') {
                $query->whereDoesntHave('profilerResult');
            }
        })
        // ->when($registrationstatus, function ($query) use ($registrationstatus) {
        //     $query->whereHas('profile', function ($q) use ($registrationstatus) {
        //         $status = ($registrationstatus == "completed") ? 1 : 0;
        //         $q->where('accountcreated',  $status);
        //     });
        // })
        ->when($this->user->isAdmin() && $this->created_in, function ($query) {
            $query->whereYear('users.created_at', $this->created_in);
        })
        ->when($this->status, function ($query) {
            if ($this->status == 'active') {
                $query->where(function ($query) {
                    $query->where("license_end_date", ">", Carbon::now())->orWhere(function ($query) {
                        $query->where('accountcreated', 0);
                    });
                });
                // if ($this->user->isAdmin()) {
                //     $query->where("license_end_date", ">", Carbon::now());
                // } else {
                //     $query->whereHas('profile', function ($q) {
                //         $q->where(function ($query) {
                //             $query->where('standard_id', '<>', 7)
                //                 ->whereRemoved(false);
                //         })->orWhere(function ($query) {
                //             $query->where('standard_id', null)
                //                 ->where('accountcreated', 0);
                //         });
                //     });
                // }
                if ($year = $this->year) {
                    $query->whereHas('profile', function ($q) use ($year) {
                        $q->whereStandardId($year);
                    });
                }
            } elseif ($this->status == 'graduated') {
                $graduateYear = $this->graduate_year;
                $query->whereHas('profile', function ($q) use ($graduateYear) {
                    if ($graduateYear) {
                        $q->where('standard_id', 7)->whereGraduateYear($graduateYear)->whereRemoved(false);
                    } else {
                        $q->where('standard_id', 7)->whereRemoved(false);
                    }
                });
            }  elseif ($this->status == 'inactive') {
                $query->where(function ($query) {
                    $query->where("license_end_date", "<", Carbon::now())->orWhereHas('profile', function ($q) {
                        $q->whereRemoved(true);
                    });
                });
                // if ($this->user->isAdmin()) {
                //     $query->where("license_end_date", "<", Carbon::now());
                // } else {
                //     $query->whereHas('profile', function ($q){
                //         $q->whereRemoved(true);
                //     });
                // }

                if ($year = $this->year) {
                    $query->whereHas('profile', function ($q) use ($year) {
                        $q->whereStandardId($year);
                    });
                }
            }
        })
        ->when($this->user->isAdmin() && $this->orgname, function ($query) {
            $query->where('organisation_id', $this->orgname);
        })
        ->when($this->user->isAdmin() && $this->orgname && $this->orgCampus, function ($query) {
            $query->whereHas('orgCampuses', function ($q) {
                $q->where('campus_id', $this->orgCampus);
            });
        })
        ->latest()->get();
        $status = $this->status;
        $user = $this->user;
        return view('students.export', compact('students', 'status', 'user'));
    }

    public function chunkSize(): int
    {
        return 200;
    }

}
