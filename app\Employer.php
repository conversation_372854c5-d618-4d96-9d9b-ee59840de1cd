<?php

namespace App;

use App\Traits\HasIntercomUserInfo;
use App\Traits\UserSessionStatsTrait;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Overtrue\LaravelFavorite\Traits\Favoriter;

class Employer extends Authenticatable
{
    use Favoriter, UserSessionStatsTrait, HasIntercomUserInfo;

    protected $table = "users";
    protected $guarded = [];

    protected $hidden = [
        'password',
        'remember_token',
        'stripe_id',
        'card_brand',
        'card_last_four',
        'trial_ends_at',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['profile'];

    protected static function booted()
    {
        static::deleting(function ($employer) {
            if ($employer->profile) {
                $employer->profile->delete();
            }
        });

        static::addGlobalScope('employer', function (Builder $builder) {
            $builder->where('role_id', Role::whereName('Employer')->value('id'));
        });
    }

    public function profile()
    {
        return $this->hasOne(Profile::class, 'user_id');
    }

    public function role()
    {
        return $this->hasOne(Role::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Students that this employer is following (only public profiles)
     */
    public function followedStudents()
    {
        return $this->belongsToMany(Student::class, 'user_follows', 'follow_by', 'following')
                    ->whereHas('profile', function ($query) {
                        $query->where('is_public', true);
                    })
                    ->withTimestamps();
    }

    /**
     * Check if this employer is following a specific student (only if public profile)
     */
    public function isFollowing($studentId)
    {
        return $this->followedStudents()
            ->where('users.id', $studentId)
            ->exists();
    }

    /**
     * Follow a student (only if their profile is public)
     */
    public function followStudent($studentId)
    {
        $student = Student::where('id', $studentId)
            ->whereHas('profile', function ($query) {
                $query->where('is_public', true);
            })->first();

        if ($student && !$this->isFollowing($studentId)) {
            $this->followedStudents()->attach($studentId);
            return true;
        }
        return false;
    }

    /**
     * Unfollow a student (only if their profile is public)
     */
    public function unfollowStudent($studentId)
    {
        if ($this->isFollowing($studentId)) {
            $this->followedStudents()->detach($studentId);
            return true;
        }
        return false;
    }

    /**
     * Toggle follow status for a student (only if their profile is public)
     */
    public function toggleFollowStudent($studentId)
    {
        if ($this->isFollowing($studentId)) {
            $this->unfollowStudent($studentId);
            return false; // Now unfollowing
        } else {
            return $this->followStudent($studentId); // Only follows if public
        }
    }

    /**
     * Get students in the employer's pipeline based on company criteria
     * This relationship includes students who match any of the following:
     * - Have submitted responses to company's modules (VWE, Skills Training, Lessons)
     * - Have gameplan industries matching company's industries
     * - Have favorited company's industry units
     *
     * OPTIMIZED: Now uses EmployerPipelineService for better performance
     */
    public function pipelineStudents()
    {
        $pipelineService = app(\App\Services\EmployerPipelineService::class);
        return $pipelineService->getPipelineStudentsQuery($this);
    }

    /**
     * Get cached pipeline student count for better performance
     *
     * @return int
     */
    public function getCachedPipelineCount(): int
    {
        $pipelineService = app(\App\Services\EmployerPipelineService::class);
        return $pipelineService->getCachedPipelineCount($this);
    }

    /**
     * Get cached top locations for pipeline students
     *
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public function getCachedTopLocations(int $limit = 3)
    {
        $pipelineService = app(\App\Services\EmployerPipelineService::class);
        return $pipelineService->getCachedTopLocations($this, $limit);
    }

    /**
     * Clear pipeline cache when employer or company data changes
     */
    public function clearPipelineCache()
    {
        $pipelineService = app(\App\Services\EmployerPipelineService::class);
        $pipelineService->clearPipelineCache($this);
    }
}
