const fs = require('fs');
const crypto = require('crypto');

const filePath = 'public/build/assets/vapp.js';

try {
    const fileBuffer = fs.readFileSync(filePath);
    const hashSum = crypto.createHash('md5');
    hashSum.update(fileBuffer);
    const hex = hashSum.digest('hex');
    fs.writeFileSync('public/build/version.txt', `v${hex}`);
    console.log(`Version written: v${hex}`);
} catch (err) {
    console.error('Error:', err.message);
}
