@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('students'))

@push('styles')
    <style>
        .bottom-btns .buttons-csv {
            display: none;
        }

        div.dt-button-background {
            z-index: 0 !important;
        }

        .dropdown-menu>.dropdown-item:hover,
        .dropdown-item:active {
            background-color: #000000 !important;
            color: white !important;
        }
    </style>
@endpush
@section('content')
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a>
            {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Search
                    </div>
                </div>
                <div class="card-block">
                    <div class="row clearfix">
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default">
                                <label>Student's name</label>
                                <input type="text" class="form-control" name="fullname" value="{{ old('fullname') }}" />
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Status</label>
                                <select class="full-width" name="status" {{-- data-init-plugin="select2" --}} id="status">
                                    <option value="active" selected="selected">Active</option>
                                    <option value="inactive" @if (old('status') == 'inactive') selected="selected" @endif title="‘Inactive’ means any student who is not currently covered by your subscription.">Inactive</option>
                                    <option value="graduated" @if (old('status') == 'graduated') selected="selected" @endif title="‘Graduated’ means any student who has finished school. These students do not count towards your subscription.">Graduated</option>
                                    <option value="pending" @if (old('status') == 'pending') selected="selected" @endif title="‘Registration Pending’ means any student who has not created their account yet. These students do not count towards your subscription.">Registration Pending</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3" id="school_year">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Year</label>
                                <select class="full-width" name="year" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @foreach ($years as $key => $year)
                                        <option value="{{ $year->id }}" @if (old('year') == $year->id) selected @endif>{{ $year->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3" id="graduate_year">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Graduated Year</label>
                                <select class="full-width" name="graduate_year" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @for ($i = 2018; $i <= date('Y'); $i++)
                                        <option @if (old('graduate_year') == $i) selected @endif>{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Gender</label>
                                <select class="full-width" name="gender" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="M" @if (old('gender') == 'M') selected @endif>Male</option>
                                    <option value="F" @if (old('gender') == 'F') selected @endif>Female</option>
                                    <option value="Other" @if (old('gender') == 'Other') selected @endif>Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default">
                                <label>Student's email</label>
                                <input type="text" class="form-control" name="email" />
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Account created in</label>
                                <select class="full-width" name="created_in" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @for ($i = 2018; $i <= date('Y'); $i++)
                                        <option @if (old('created_in') == $i) selected @endif>{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>School</label>
                                <select class="full-width" name="school" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @foreach ($schools as $school)
                                        <option value="{{ $school->id }}">{{ $school->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3" id="campus-filter" style="display:none;">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Campus</label>
                                <select class="full-width" id="campusIds" name="campus" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Organisation</label>
                                <select class="full-width" name="organisation" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @foreach ($organisations as $organisation)
                                        <option value="{{ $organisation->id }}">{{ $organisation->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3" id="orgCampus-filter" style="display:none;">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Campus</label>
                                <select class="full-width" id="orgCampusIds" name="orgCampus" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Profiling</label>
                                <select class="full-width" name="profiler" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="completed" @if (old('profiler') == 'completed') selected="selected" @endif>Completed</option>
                                    <option value="incomplete" @if (old('profiler') == 'incomplete') selected="selected" @endif>Incomplete</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Video Profiling</label>
                                <select class="full-width" name="video_profiling" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="completed" @if (old('video_profiling') == 'completed') selected="selected" @endif>Completed</option>
                                    <option value="in_progress" @if (old('video_profiling') == 'in_progress') selected="selected" @endif>In Progress</option>
                                    <option value="incomplete" @if (old('video_profiling') == 'incomplete') selected="selected" @endif>Incomplete</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>State</label>
                                <select class="full-width" name="state" data-init-plugin="select2" data-minimum-results-for-search="Infinity">
                                    <option value="">Any</option>
                                    @foreach ($states as $state)
                                        <option value="{{ $state->id }}">{{ $state->name }}</option>
                                    @endforeach

                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>School Type</label>
                                <select class="full-width" name="type" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="Independent">Independent</option>
                                    <option value="Catholic">Catholic</option>
                                    <option value="Government">Government</option>
                                </select>
                            </div>
                        </div>
                        {{-- <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Registration</label>
                                <select class="full-width" name="registration" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="completed" @if (old('registration') == 'completed') selected="selected" @endif>Completed</option>
                                    <option value="incomplete" @if (old('registration') == 'incomplete') selected="selected" @endif>Incomplete</option>
                                </select>
                            </div>
                        </div> --}}
                        {{-- <div class="col-md-4 col-lg-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Active/Inactive</label>
                                <select class="full-width" name="active_inactive" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                        </div> --}}
                        <div class="col-md-12 text-right">
                            <button class="btn btn-primary mt-3" id="btnSearch" type="button">Search</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Students
                    </div>
                    <div class="card-controls">
                        <ul>
                            <li>
                                {{-- <button type="button" data-target="#modalStudentAdd" data-toggle="modal" id="btnFillSizeToggler" class="btn btn-primary btn-cons">
                                    <i class="fa fa-plus"></i> Add New
                                </button> --}}
                                {{-- <a href="{{ route('yearRollover')}}" data-token="{{csrf_token()}}" onclick="return confirm('Are you sure?')" class="btn btn-primary btn-cons">
                                    Year Rollover
                                </a> --}}
                                <a class="btn btn-primary d-none" id="exportStudents" href="#">Export All <i class="fa fa-download"></i></a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-block">
                    <form method="POST" action="{{ url('students/bulkAction') }}" id="formTable" onkeydown="return event.key != 'Enter';">
                        @csrf
                        <div id="checkerror"></div>
                        @include('students.modals.bulkAction')
                        <table class="table table-hover custom-datatable no-footer" id="students-table">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="checkbox check-primary">
                                            <input type="checkbox" id="checkAll">
                                            <label for="checkAll">Select</label>
                                        </div>
                                    </th>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Email</th>
                                    <th>School</th>
                                    <th>School Type</th>
                                    <th>School State</th>
                                    <th id="campus-col">School Campus</th>
                                    <th>Year</th>
                                    <th>Graduated Year</th>
                                    <th>Organisation</th>
                                    <th>Organisation Campus</th>
                                    <th>Gender</th>
                                    <th>Tags</th>
                                    <th>Parents</th>
                                    <th>Have registered</th>
                                    <th>Profiling</th>
                                    <th>Video Profiling</th>
                                    <th class="dontexport">Game Plan</th>
                                    <th class="dontexport">Timeline</th>
                                    <th class="dontexport">Notes</th>
                                    <th class="dontexport">CVs</th>
                                    <th>Last seen</th>
                                    <th>Login count</th>
                                    <th>Avg login duration</th>
                                    <th>Avg login duration (in sec)</th>
                                    <th>Account created</th>
                                    <th>Subscription end date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        @include('students.modals.addstudent')
        @include('students.modals.editstudent')
        @include('students.modals.editchild')
        @include('students.modals.import')
    @endpush
    @push('scripts')
        <script>
            $(document).ready(function() {
                var oTable = $('#students-table').DataTable({
                    order: [
                        [1, "asc"]
                    ],
                    // orderable: true,
                    stateSave: true,
                    fixedHeader: true,
                    processing: true,
                    serverSide: true,
                    searching: false,
                    stateSave: true,
                    //dom: 'Bfrtip',
                    dom: "<'row'<'col-md-12'Bl>>" + "<'checkError text-danger'>" + "<'table-responsive mb-4'rt><'checkError text-danger mt-2'><'mt-2 bottom-btns'B><'row'<'col-md-12'pi>>",
                    "autoWidth": false,
                    "lengthMenu": [
                        [10, 25, 50, 100 /* , -1 */ ],
                        [10, 25, 50, 100 /* , "All" */ ] // Not able to handle the request showing All students at once.
                    ],
                    buttons: [{
                            extend: 'collection',
                            text: 'Add <i class="fa fa-plus">',
                            className: 'btn btn-primary btn-cons',
                            buttons: [{
                                    text: 'Add Individual Student',
                                    className: 'bulkImport',
                                    action: function(e, dt, node, config) {
                                        $('#modalStudentAdd').modal('show');
                                    }
                                },
                                {
                                    text: 'Import Group of Students',
                                    className: 'bulkImport',
                                    action: function(e, dt, node, config) {
                                        $('#bulkImport').modal('show');
                                    }
                                }
                            ]
                        },
                        {
                            text: 'Bulk action',
                            className: 'btn btn-primary btn-cons bulk-action',
                        },
                        {
                            text: 'Export All <i class="fa fa-download"></i>',
                            className: 'btn btn-primary btn-cons',
                            action: function(e, dt, node, config) {
                                jQuery("#exportStudents")[0].click();
                            }
                        },
                        // {
                        //     extend: 'csv',
                        //     text: '<i class="fa fa-download"></i> CSV',
                        //     filename: 'TCD-Students_list',
                        //     className: 'btn btn-primary btn-cons',
                        //     exportOptions: {
                        //         columns: 'th:not(.dontexport)'
                        //     }
                        // }
                    ],
                    ajax: {
                        url: 'students/getdata',
                        beforeSend: function(request) {
                            request.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
                        },
                        "type": "POST",
                        data: function(d) {
                            d.name = $('input[name=fullname]').val();
                            d.email = $('input[name=email]').val();
                            d.year = $('select[name=year]').val();
                            d.gender = $('select[name=gender]').val();
                            d.created_in = $('select[name=created_in]').val();
                            d.schoolname = $('select[name=school]').val();
                            d.orgname = $('select[name=organisation]').val();
                            d.campus = $('select[name=campus]').val();
                            d.orgCampus = $('select[name=orgCampus]').val();
                            d.profiler = $('select[name=profiler]').val();
                            d.video_profiling = $('select[name=video_profiling]').val();
                            d.graduate_year = $('select[name=graduate_year]').val();
                            d.active_inactive = $('select[name=active_inactive]').val();
                            d.status = $('select[name=status]').val();
                            d.email = $('input[name=email]').val();
                            d.registration = $('select[name=registration]').val();
                            d.state = $('select[name=state]').val();
                            d.type = $('select[name=type]').val();
                            return d;
                        }
                    },
                    columns: [{
                            data: 'select',
                            name: 'select',
                            orderable: false,
                        },
                        {
                            data: 'firstname',
                            name: 'firstname',
                        },
                        {
                            data: 'lastname',
                            name: 'lastname',
                        },
                        {
                            data: 'email',
                            name: 'email',
                        },
                        {
                            data: 'schoolname',
                            name: 'schoolname',
                            orderable: false,
                        },

                        {
                            data: 'type',
                            name: 'type',
                        },
                        {
                            data: 'state',
                            name: 'state',
                        },
                        {
                            data: 'schoolCampus',
                            name: 'schoolCampus',
                            orderable: false,
                        },
                        {
                            data: 'year_title',
                            name: 'standard_id',
                        },
                        {
                            data: 'graduate_year',
                            name: 'graduate_year',
                        },
                        {
                            data: 'orgname',
                            name: 'orgname',
                            orderable: false,
                        },
                        {
                            data: 'orgCampus',
                            name: 'orgCampus',
                            orderable: false,
                        },
                        {
                            data: 'gender',
                            name: 'gender',
                        },
                        {
                            data: 'tags',
                            name: 'tags',
                            orderable: false,
                        },
                        {
                            data: 'parents',
                            name: 'parents',
                            orderable: false,
                        },
                        {
                            data: 'p_accountcreated',
                            name: 'accountcreated',
                            searchable: false,
                        },
                        {
                            data: 'profiling',
                            name: 'profiling',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'video_profiling',
                            name: 'video_profiling',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'gameplan',
                            name: 'gameplan',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'timeline',
                            name: 'timeline',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'notes',
                            name: 'notes',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'cvs',
                            name: 'cvs',
                            searchable: false,
                            orderable: false,
                            className: "text-center"
                        },
                        {
                            data: 'lastseen',
                            name: 'lastseen',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'logincount',
                            name: 'logincount',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'avgduration',
                            name: 'avgduration',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'avgdurationsec',
                            name: 'avgdurationsec',
                            searchable: false,
                            orderable: false,
                            visible: false,
                        },
                        {
                            data: 'account_created',
                            name: 'created_at',
                        },
                        {
                            data: 'subscription_end_date',
                            name: 'subscription_end_date',
                            orderable: false,
                        },
                        {
                            data: 'action',
                            name: 'action',
                            searchable: false,
                            orderable: false,
                        },
                    ],
                    "drawCallback": function(settings) {
                        var api = this.api();
                        // console.log( api.column(3).data() );
                        if (jQuery(('select[name=school]')).val()) {
                            api.column(7).visible(jQuery('#campus-filter').is(":visible"))
                        } else {
                            api.column(7).visible(true)
                        }

                        if (jQuery('#graduate option:selected').val() == 'yes') {
                            api.column(8).visible(false)
                            api.column(9).visible(true)
                        } else {
                            api.column(8).visible(true)
                            api.column(9).visible(false)
                        }

                        // Export Students With Filters Value
                        name = $('input[name=fullname]').val();
                        year = $('select[name=year]').val();
                        gender = $('select[name=gender]').val();
                        campus = $('select[name=campus]').val();
                        status = $('select[name=status]').val();
                        profiler = $('select[name=profiler]').val();
                        registration = $('select[name=registration]').val();
                        email = $('input[name=email]').val();
                        graduate_year = $('select[name=graduate_year]').val();
                        created_in = $('select[name=created_in]').val();
                        schoolname = $('select[name=school]').val();
                        orgname = $('select[name=organisation]').val();
                        orgCampus = $('select[name=orgCampus]').val();
                        state = $('select[name=state]').val();
                        type = $('select[name=type]').val();

                        jQuery("#exportStudents").attr("href", "/student/export?fullname=" + name + "&year=" + year + "&gender=" + gender + "&campus=" + campus + "&status=" + status + "&profiler=" + profiler + "&registration=" + registration + "&email=" + email + "&graduate_year=" + graduate_year + "&status=" + status + "&created_in=" + created_in + "&schoolname=" + schoolname + "&orgname=" + orgname + "&orgCampus=" + orgCampus + "&state=" + state + "&type=" + type + "");

                        laravel.initialize();
                    }
                });

                jQuery(document).on('change', "#checkAll", function() {
                    jQuery('input[name="ids[]"]:checkbox').prop('checked', this.checked);
                });

                jQuery("#school_year").show();
                jQuery("#graduate_year").hide();

                jQuery('#status').on('change', function() {
                    if (this.value == 'graduated') {
                        jQuery("#graduate_year").show();
                        jQuery("#school_year").hide();
                    } else {
                        jQuery("#school_year").show();
                        jQuery("#graduate_year").hide();
                    }
                });

                jQuery('#selectChangeYear').on('select2:select', function(e) {
                    var selectedStudents = jQuery('input[name="ids[]"]:checked').length;
                    if (selectedStudents > 0) {
                        selctedYear = jQuery(this).select2('data')[0].text;
                        confirmation = 'Are you sure you want to change year for ' + selectedStudents + ' student(s) to ' +
                            selctedYear + '?';
                        if (confirm(confirmation)) {
                            jQuery("#formTable").submit();
                        } else {
                            jQuery(this).val('').trigger("change.select2");
                        }
                    } else {
                        alert('No student selected!');
                        jQuery(this).val('').trigger("change.select2");
                    }
                });

                jQuery('#formTable select[name=school]').change(function() {
                    jQuery('#formTable select[name="campus"]').html('<option value="" selected>Any</option>');
                    if (jQuery(this).val()) {
                        jQuery.ajax({
                            type: "get",
                            url: "{{ route('getSchoolCampuses') }}",
                            data: {
                                id: jQuery(this).val()
                            },
                            dataType: 'json',
                            success: function(response) {
                                if (Object.keys(response).length) {
                                    jQuery.each(response, function(id, name) {
                                        jQuery('#formTable select[name="campus"]').append('<option value="' + id + '">' + name + '</option>')
                                    });
                                    jQuery('#campus-filter').show();
                                } else {
                                    jQuery('#campus-filter').hide();
                                }
                            },
                        });
                    } else {
                        jQuery('#campus-filter').hide();
                    }
                });

                jQuery('#formTable select[name=organisation]').change(function() {
                    jQuery('#formTable select[name="orgCampus"]').html('<option value="" selected>Any</option>');
                    if (jQuery(this).val()) {
                        jQuery.ajax({
                            type: "get",
                            url: "{{ route('getOrganisationCampuses') }}",
                            data: {
                                id: jQuery(this).val()
                            },
                            dataType: 'json',
                            success: function(response) {
                                if (Object.keys(response).length) {
                                    jQuery.each(response, function(id, name) {
                                        jQuery('#formTable select[name="orgCampus"]').append('<option value="' + id + '">' + name + '</option>')
                                    });
                                    jQuery('#orgCampus-filter').show();
                                } else {
                                    jQuery('#orgCampus-filter').hide();
                                }
                            },
                        });
                    } else {
                        jQuery('#orgCampus-filter').hide();
                    }
                });

                // jQuery("#bulk_graduated_year").hide();
                jQuery('select[name="withselected"]').on('change', function() {
                    jQuery("#yearChange").hide();
                    jQuery("#active_inactive").hide();
                    jQuery("#delete_nominee").hide();
                    jQuery("#bulk_graduated_year").hide();
                    if (this.value == 'Make Active/Inactive') {
                        jQuery("#active_inactive").show();

                    } else if (this.value == 'Change year') {
                        jQuery("#yearChange").show();
                        if (jQuery('#bulk_year').val() == 7) {
                            jQuery("#bulk_graduated_year").show();
                        }
                    } else if (this.value == 'Reset Profiling') {
                        jQuery("#delete_nominee").show();
                    }
                });
                jQuery('#bulk_year').on('change', function() {
                    if (this.value == 7) {
                        jQuery("#bulk_graduated_year").show();
                    } else {
                        jQuery("#bulk_graduated_year").hide();
                    }
                });

                jQuery('#formTable').validate({
                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length || element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                            if (element.attr('name') == 'ids[]') {
                                error.appendTo(jQuery('#checkerror'))
                            } else {
                                error.insertAfter(element.parent());
                            }
                        } else if (element.hasClass('select2-hidden-accessible')) {
                            // error.insertAfter(element.next('span'));
                            element.closest('.form-group').append(error);

                        } else {
                            // else just place the validation message immediatly after the input
                            error.insertAfter(element.parent());
                        }
                    },
                    focusInvalid: false,
                    rules: {
                        'ids[]': 'required',
                        withselected: 'required',
                        standard: {
                            required: function() {
                                return (jQuery('select[name=withselected]').val() == 'Change year');
                            }
                        },
                        bulk_graduated_year: {
                            required: function() {
                                return (jQuery('#bulk_year').val() == 7);
                            }
                        },
                        nominees: {
                            required: function() {
                                return (jQuery('select[name=withselected]').val() == 'Reset Profiling');
                            }
                        },
                    },
                    messages: {
                        'ids[]': {
                            required: 'Please select at least one student.'
                        }
                    },
                    // submitHandler: function(form, event) {
                    //     event.preventDefault();
                    //     if (!confirm("Do you really want to do this?")) {
                    //         return false;
                    //     }
                    //     form.submit();
                    // }
                });

                $("#status").select2({
                    escapeMarkup: function(markup) {
                        return markup;
                    },
                });

                $('#btnSearch').on('click', function(e) {
                    oTable.draw();
                    e.preventDefault();
                });

                jQuery(".bulk-action").click(function() {
                    if (jQuery('input[name="ids[]"]:checked').length > 0) {
                        jQuery('#bulkAction').modal('show');
                    } else {
                        jQuery('.checkError').text('Please select at least one student.');
                    }
                });

                jQuery(".bulkImport").click(function() {
                    jQuery('#bulkImport').modal('show');
                });
            });
        </script>
    @endpush
@endsection
