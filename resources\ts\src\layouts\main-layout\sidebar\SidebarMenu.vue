<template>
    <!--begin::sidebar menu-->
    <div class="aside-menu flex-column-fluid py-1">
        <!--begin::Menu wrapper-->
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-20" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
            <!--begin::Menu-->
            <div id="#kt_app_sidebar_menu" class="menu menu-column menu-title-gray-700 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500 fw-semibold" data-kt-menu="true">
                <template v-for="(item, i) in MenuConf" :key="i">
                    <div v-if="item.heading" class="menu-item pt-5" :data-heading="item.heading">
                        <div class="menu-content">
                            <span class="menu-heading fw-bold text-uppercase fs-7">
                                {{ item.heading }}
                            </span>
                        </div>
                    </div>
                    <template v-for="(menuItem, j) in item.pages" :key="j">
                        <template v-if="menuItem.heading">
                            <div
                                class="menu-item py-5"
                                :data-heading="menuItem.heading"
                                :data-route="menuItem.route"
                            >
                                <d v-if="menuItem.showPopup">Show Popup</d>
                                <!-- Non-vue routes -->
                                <a class="menu-link menu-center flex-column" active-class="active" :href="menuItem.route" v-else-if="menuItem.isExternal">
                                    <span v-if="menuItem.svgIcon || menuItem.fontIcon" class="menu-icon m-0">
                                        <i v-if="sidebarMenuIcons === 'font'" :class="menuItem.fontIcon" class="bi fs-1"></i>
                                        <span v-else-if="sidebarMenuIcons === 'svg'" class="svg-icon svg-icon-2">
                                            <inline-svg :src="menuItem.svgIcon" />
                                        </span>
                                    </span>
                                    <span class="menu-title">{{
                                         menuItem.heading
                                    }}</span>
                                </a>

                                <!-- Disabled Menu -->
                                <a class="menu-link menu-center flex-column " active-class="active" href="javascript:void(0);"  tabindex="-1" aria-disabled="true" v-else-if="menuItem.disabled && menuItem.heading === 'Company'"
                                 title="Coming Soon: Manage your company page">
                                    <span v-if="menuItem.svgIcon || menuItem.fontIcon" class="menu-icon m-0">
                                        <i v-if="sidebarMenuIcons === 'font'" :class="menuItem.fontIcon" class="bi fs-1"></i>
                                        <span v-else-if="sidebarMenuIcons === 'svg'" class="svg-icon svg-icon-2">
                                            <inline-svg :src="menuItem.svgIcon" />
                                        </span>
                                    </span>
                                    <span class="menu-title">{{
                                         menuItem.heading
                                    }}</span>
                                </a>


                                <!-- Vue routes -->
                                <router-link class="menu-link menu-center flex-column" active-class="active" :to="menuItem.route" v-else>
                                    <span v-if="menuItem.svgIcon || menuItem.fontIcon" class="menu-icon m-0">
                                        <i v-if="sidebarMenuIcons === 'font'" :class="menuItem.fontIcon" class="bi fs-1"></i>
                                        <span v-else-if="sidebarMenuIcons === 'svg'" class="svg-icon svg-icon-2">
                                            <inline-svg :src="menuItem.svgIcon" />
                                        </span>
                                    </span>
                                    <span class="menu-title">{{
                                        menuItem.heading
                                    }}</span>
                                </router-link>
                            </div>
                        </template>
                        <div v-if="menuItem.sectionTitle && menuHeading(menuItem.sectionTitle)" :class="{ show: hasActiveChildren(menuItem.route) }" class="menu-item py-5" data-kt-menu-trigger="click" :data-heading="menuItem.sectionTitle" :data-route="menuItem.route">
                            <span class="menu-link menu-center flex-column">
                                <span v-if="menuItem.svgIcon || menuItem.fontIcon" class="menu-icon m-0"> <i v-if="sidebarMenuIcons === 'font'" :class="menuItem.fontIcon" class="bi fs-1"></i> <span v-else-if="sidebarMenuIcons === 'svg'" class="svg-icon svg-icon-2" :class="menuItem.svgClass"> <inline-svg :src="menuItem.svgIcon" /> </span> </span>
                                <span class="menu-title">{{ menuItem.sectionTitle }} </span>
                                 <!-- main menu heading -->
                            </span>
                            <div :class="{ show: hasActiveChildren(menuItem.route) }" class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0">
                                <template v-for="(item2, k) in menuItem.sub" :key="k">
                                    <div v-if="(item2.showPopup == true)" class="menu-item">
                                        <span data-bs-toggle="modal" data-bs-target="#kt_modal_InviteChild" class="menu-link" active-class="active" v-if="(item2.isExternal)">
                                            <span class="menu-title">
                                                {{ item2.heading }}
                                                <span class="svg-icon svg-icon-primary svg-icon" v-if="item2.heading != 'Add Child +'">
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="19.5" height="19.5" viewBox="0 0 20 20" version="1.1">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <mask fill="white">
                                                                <use xlink:href="#path-1" />
                                                            </mask>
                                                            <g />
                                                            <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#707070" />
                                                        </g>
                                                    </svg>
                                                </span>
                                            </span>
                                        </span>
                                        <router-link class="menu-link" active-class="active" :to="item2.route" v-else>
                                            <span class="menu-title">{{
                                                item2.heading
                                            }}</span>
                                        </router-link>
                                    </div>
                                    <div v-if="(item2.hasNotAccess == true)" class="menu-item">
                                        <span class="menu-link" active-class="active" v-if="(item2.isExternal)">
                                            <span class="menu-title lh-0">{{
                                                item2.heading
                                            }}
                                                <span class="svg-icon svg-icon-primary svg-icon-1x" style="padding-left: 5px;">
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <mask fill="white">
                                                                <use xlink:href="#path-1" />
                                                            </mask>
                                                            <g />
                                                            <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" />
                                                        </g>
                                                    </svg>
                                                </span>
                                            </span>
                                        </span>
                                        <router-link class="menu-link" active-class="active" :to="item2.route" v-else>
                                            <span class="menu-title">{{
                                                item2.heading
                                            }}</span>
                                        </router-link>
                                    </div>
                                    <div v-if="!item2.showPopup && !item2.hasNotAccess" class="menu-item">
                                        <div v-if="(item2.isParent)">
                                            <a class="menu-link" active-class="active" :href="item2.route" v-if="(item2.isProcessed)">
                                                <span class="menu-title">{{
                                                    item2.heading
                                                }}</span>
                                            </a>

                                            <span class="menu-link menu-title" v-if="(!item2.isProcessed && item2.accountCreated)">
                                                <div class="navmenu-tooltip ">
                                                    {{ item2.heading }}
                                                    <span class="tooltiptext">
                                                        Pending account connection {{ item2.heading }} has an account but has not connected it to yours yet. You can reinvite them to do this from your profile.
                                                    </span>
                                                </div>
                                            </span>

                                            <span class="sub-menu-padding menu-title" v-if="(!item2.isProcessed && !item2.accountCreated)">
                                                <div class="navmenu-tooltip ">
                                                    {{ item2.heading }}
                                                    <span class="tooltiptext">
                                                        Pending account creation {{ item2.heading }} has received an invitation to create their account and connect it to yours, but has not set this up yet. You can reinvite them to do this from your profile.
                                                    </span>
                                                </div>
                                            </span>
                                        </div>


                                        <template v-if="menuLink(item2.heading)">
                                            <!-- Non vue links -->
                                            <a class="menu-link" active-class="active" :href="item2.route" v-if="item2.isExternal" :target="item2.inNewTab ? '_blank' : ''">
                                                <span class="menu-title">{{ item2.heading }}</span>
                                            </a>
                                            <!-- Non vue links -->

                                            <!-- vuelinks -->
                                            <router-link class="menu-link" active-class="active" :to="item2.route" v-if="(!item2.isExternal && !item2.isParent)">
                                                <!-- <span class="menu-bullet"> <span class="bullet bullet-dot"></span> </span> -->
                                                <span class="menu-title">{{ item2.heading }}</span>
                                            </router-link>
                                        </template>
                                        <!-- vuelinks -->

                                        <!-- <template v-if="item2.heading == 'Subject Selections' && currentUser.hasSubjectsSelectionAccess">
                        <a
                          class="menu-link"
                          active-class="active"
                          :href="item2.route"
                          v-if="item2.isExternal"
                          :target="item2.inNewTab && '_blank'"
                        >
                          <span class="menu-title">{{ item2.heading }}</span>
                        </a>
                    </template>
                    <template v-else-if="item2.heading != 'Subject Selections'">
                        <a
                          class="menu-link"
                          active-class="active"
                          :href="item2.route"
                          v-if="item2.isExternal"
                          :target="item2.inNewTab && '_blank'"
                        >
                          <span class="menu-title">{{ item2.heading }}</span>
                        </a>
                    </template> -->
                  </div>
                  <div
                    v-if="item2.sectionTitle"
                    :class="{ show: hasActiveChildren(item2.route) }"
                    class="menu-item menu-dropdown"
                    data-kt-menu-sub="accordion"
                    data-kt-menu-trigger="click"
                  >
                    <span class="menu-link">
                      <!-- <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                        </span> -->
                      <span class="menu-title">{{ item2.sectionTitle }}</span>
                      <span class="menu-arrow"></span>
                    </span>
                    <div :class="{ show: hasActiveChildren(item2.route) }" class="menu-sub menu-sub-accordion">
                      <template v-for="(item3, k) in item2.sub" :key="k">
                        <div v-if="item3.heading" class="menu-item">
                          <router-link class="menu-link" active-class="active" :to="item3.route">
                            <!-- <span class="menu-bullet">
                              <span class="bullet bullet-dot"></span>
                            </span> -->
                            <span class="menu-title">{{ item3.heading }}</span>
                          </router-link>
                        </div>
                      </template>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </template>
      </div>
      <!--end::Menu-->
    </div>
    <!--end::Menu wrapper-->
  </div>
  <!--end::sidebar menu-->
</template>

<script>
import { defineComponent, onMounted, onBeforeUnmount, ref } from "vue";
import { useRoute } from "vue-router";
import MainMenuConfig from "@/core/config/MainMenuConfig";
import StudentMenuConfig from "@/core/config/StudentMenuConfig";
import TeacherMenuConfig from "@/core/config/TeacherMenuConfig";
import ManagerTeacherMenuConfig from "@/core/config/ManagerTeacherMenuConfig";
import ContentTeacherMenuConfig from "@/core/config/ContentTeacherMenuConfig";
import PrimaryTeacherMenuConfig from "@/core/config/PrimaryTeacherMenuConfig";
import PrimaryTeacherFullMenuConfig from "@/core/config/PrimaryTeacherFullMenuConfig";
import ParentLimitedMenuConfig from "@/core/config/ParentLimitedMenuConfig";
import ParentPremiumMenuConfig from "@/core/config/ParentPremiumMenuConfig";
import EmployerMenuConfig from "@/core/config/EmployerMenuConfig";
import { sidebarMenuIcons } from "@/core/helpers/config";
import { useI18n } from "vue-i18n";
import { useStore } from "vuex";
import axios from "axios";
import Intercom from "@intercom/messenger-js-sdk";
import ApiService from "@/core/services/ApiService";

export default defineComponent({
  name: "sidebar-menu",
  components: {},

  setup() {
    const { t, te } = useI18n();
    const route = useRoute();
    const store = useStore();
    const currentUser = store.getters.currentUser;
    const isUserAuthenticated = store.getters.isUserAuthenticated;
    var MenuConf = "";
    if (currentUser) {
      if (currentUser.isStudent || currentUser.studentView) {
        MenuConf = StudentMenuConfig;
      } else if (currentUser.isTeacher && !currentUser.studentView && currentUser.isPrimaryTeacher) {
        if (currentUser.hasFullAccess) {
          MenuConf = PrimaryTeacherFullMenuConfig;
        } else {
          MenuConf = PrimaryTeacherMenuConfig;
        }
      } else if (currentUser.isTeacher && !currentUser.studentView && currentUser.isSecondaryTeacher) {
        if (currentUser.hasAccess == "Full" || currentUser.hasAccess == "Lead Administrator") {
          MenuConf = TeacherMenuConfig;
        } else if (currentUser.hasAccess == "Manager") {
          MenuConf = ManagerTeacherMenuConfig;
        } else if (currentUser.hasAccess == "Content") {
          MenuConf = ContentTeacherMenuConfig;
        }
      } else if (currentUser.isParent && currentUser.hasLimitedAccess) {
        MenuConf = ParentLimitedMenuConfig;
      } else if (currentUser.isParent && currentUser.hasPremiumAccess) {
        MenuConf = ParentPremiumMenuConfig;
        MenuConf[0]["pages"].forEach((element, index) => {
          if (typeof element.sectionTitle != "undefined" && element.sectionTitle == "Accounts" && currentUser.children.length) {
            var children = [];
            currentUser.children.forEach((child) => {
              children.push({
                heading: child.name && child.name.length ? child.name : child.email,
                isParent: true,
                isProcessed: child.processed,
                accountCreated: child.accountcreated,
                route: "/profiles/edit/" + child.id,
              });
            });
            element.sub.forEach((menu) => {
              children.push(menu);
            });
            MenuConf[0]["pages"][index]["sub"] = children;
          }
        });
      } else if (currentUser.isEmployer) {
        MenuConf = EmployerMenuConfig;
      } else {
        MenuConf = MainMenuConfig;
      }
    }
    const scrollElRef = (ref < null) | (HTMLElement > null);

    onMounted(() => {
      if (scrollElRef.value) {
        scrollElRef.value.scrollTop = 0;
      }

      if (isUserAuthenticated && !currentUser.isAdmin) {
        saveSession();
        setInterval(function () {
          saveSession();
        }, 60000);
      }

      checkIntercomStatus();
    });

    onBeforeUnmount(() => {
      if (window.Intercom) {
        window.Intercom("shutdown");
      }
    });

    const saveSession = () => {
      axios.get("updateUserSession");
    };

    const translate = (text) => {
      if (te(text)) {
        return t(text);
      } else {
        return text;
      }
    };

    const hasActiveChildren = (match) => {
      return route.path.indexOf(match) !== -1;
    };

    // A computed property that returns the menu link object
    const menuHeading = (heading) => {
      if (currentUser.isStudent || currentUser.studentView) {
        if (heading == "Explore") {
          return currentUser.hasIndustriesAccess || currentUser.hasEMagazineAccess;
        } else if (heading == "Tasks") {
          return currentUser.hasLessonsAccess || currentUser.hasVweAccess || currentUser.hasSkillsTrainingAccess || currentUser.hasWhsAccess || currentUser.hasMyPathAccess;
        } else if (heading == "Tools") {
          return (
            currentUser.hasJobFinderAccess ||
            currentUser.hasScholarshipFinderAccess ||
            currentUser.hasResumeBuilderAccess ||
            currentUser.hasCourseFinderAccess ||
            currentUser.hasEPortfolioAccess ||
            currentUser.hasSubjectSelectionsAccess
          );
        } else if (heading == "Connect") {
          return currentUser.hasNoticeboardAccess;
        } else if (heading == "Support") {
          return currentUser.isStudent ? currentUser.hasHelpCentreAccess : true;
        }
      }
      return true;
    };

    // A computed property that returns the menu link object
    const menuLink = (heading) => {
      let headings = [
        "Industries",
        "e-Magazine",
        "Video Profiling",
        "My Paths",
        "Career Profiling",
        "Lessons",
        "Lessons Manage",
        "Virtual Work Experience",
        "Skills Training",
        "Work, Health & Safety",
        "Job Finder",
        "Scholarship Finder",
        "Resume Builder",
        "Course Finder",
        "ePortfolio",
        "Subject Selections",
        "Noticeboard",
        "Help Centre",
      ];
      // Check if the user has access to the subject selections
      if (heading === "Industries" && currentUser.hasIndustriesAccess) {
        return true;
      } else if (heading === "e-Magazine" && currentUser.hasEMagazineAccess) {
        return true;
      } else if (heading === "Career Profiling" && currentUser.hasCareerProfilingAccess) {
        return true;
      } else if (heading === "Video Profiling" && currentUser.hasVideoProfilingAccess) {
        return true;
      } else if (heading === "My Paths" && currentUser.hasMyPathAccess) {
        return true;
      } else if ((heading === "Lessons" || heading == "Lessons Manage") && currentUser.hasLessonsAccess) {
        return true;
      } else if (heading === "Virtual Work Experience" && currentUser.hasVweAccess) {
        return true;
      } else if (heading === "Skills Training" && currentUser.hasSkillsTrainingAccess) {
        return true;
      } else if (heading === "Work, Health & Safety" && currentUser.hasWhsAccess) {
        return true;
      } else if (heading === "Job Finder" && currentUser.hasJobFinderAccess) {
        return true;
      } else if (heading === "Scholarship Finder" && currentUser.hasScholarshipFinderAccess) {
        return true;
      } else if (heading === "Resume Builder" && currentUser.hasResumeBuilderAccess) {
        return true;
      } else if (heading === "Course Finder" && currentUser.hasCourseFinderAccess) {
        return true;
      } else if (heading === "ePortfolio" && currentUser.hasEPortfolioAccess) {
        return true;
      } else if (heading === "Subject Selections" && currentUser.hasSubjectSelectionsAccess) {
        return true;
      } else if (heading === "Noticeboard" && currentUser.hasNoticeboardAccess) {
        return true;
      } else if (heading === "Help Centre" && currentUser.hasHelpCentreAccess) {
        return true;
      } else if (!headings.includes(heading)) {
        return true;
      }
      // Return null if none of the conditions are met
      return false;
    };

    // INTERCOM - START
    const bootIntercom = () => {
      if (currentUser.intercom) {
        Intercom({
          app_id: import.meta.env.VITE_INTERCOM_APP_ID,
          region: import.meta.env.VITE_INTERCOM_APP_REGION,
          user_id: currentUser.intercom.uuid, // IMPORTANT: Replace "user.id" with the variable you use to capture the user's ID
          name: currentUser.name, // IMPORTANT: Replace "user.name" with the variable you use to capture the user's name
          email: currentUser.email, // IMPORTANT: Replace "user.email" with the variable you use to capture the user's email
        });
      } else {
        console.log("No user intercom data found: VUE");
      }
    };

    const checkIntercomStatus = async () => {
      if (currentUser.intercom == null) {
        console.log("Intercom is Disabled on VUE , No user intercom data found");
        return;
      }

      try {
        const { data } = await ApiService.get(`/api`, `intercom-status`);

        if (data.is_enabled) {
          bootIntercom();
          console.log("Intercom is Enabled on VUE");
        } else {
          console.log("Intercom is Disabled on VUE");
        }
      } catch (error) {
        console.error("Error fetching Intercom status VUE:", error);
      }
    };
    // INTERCOM - END

    return {
      hasActiveChildren,
      MainMenuConfig,
      sidebarMenuIcons,
      translate,
      currentUser,
      MenuConf,
      menuHeading,
      menuLink,
    };
  },
});
</script>
<style>
.aside-menu .menu > .menu-item > .menu-link .menu-title {
  color: #707070;
}

.menu-item .menu-link .menu-icon .svg-icon svg path {
  fill: #707070;
}

.aside-menu .menu > .menu-item:not(.here) > .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg path,
.menu-item .menu-link.active .menu-icon .svg-icon svg path,
.show .menu-link .menu-icon .svg-icon svg path {
  fill: #fff;
}

.text-left {
  text-align: left;
}

.p-relative {
  position: relative;
}

.navmenu-tooltip {
  position: relative;
  display: inline-block;
  color: #5e6278;
  cursor: pointer;
}

.navmenu-tooltip:hover {
  color: #000;
}

.navmenu-tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #000;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.navmenu-tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #000 transparent transparent transparent;
}

.navmenu-tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.sub-menu-padding {
  display: flex;
  padding: 0.65rem 1rem;
}

.svg-icon.svg-icon-2.connect-icon svg {
  height: 2rem !important;
  width: 3rem !important;
}
</style>
