<template>
    <div class="d-flex flex-column flex-lg-row flex-column-fluid bg-black">
        <div class="d-flex flex-column flex-lg-row-fluid w-lg-50 p-10 order-2 order-lg-1 bgc-md-white flex-fill">
            <div class="d-flex bg-white flex-center flex-column flex-lg-row-fluid rounded">
                <div class="w-lg-500px p-10">
                    <form @submit.prevent="handleSubmit">
                        <div class="text-center mb-5">
                            <h1 class="text-gray-900 fw-bolder mb-3">Get Started</h1>
                            <div class="text-gray-500 fw-semibold fs-6 mt-10">
                                Enter your <strong>{{ instituteDetails.name }}</strong> email
                                address to create an account
                            </div>
                        </div>
                        <div class="mb-10">
                            <input v-model="email" type="email" class="form-control form-control-lg rounded-1"
                                placeholder="Email" @input="emailError = null" />
                            <div v-if="emailError" class="text-danger mt-1">
                                {{ emailError }}
                            </div>
                        </div>
                        <!-- <div class="mb-10 form-check">
              <input
                v-model="acceptedPrivacyPolicy"
                type="checkbox"
                class="form-check-input"
                id="privacyPolicy"
              />
              <label class="form-check-label" for="privacyPolicy">
                I have read and accepted the <a class="form-check-label" :href="instituteDetails.privacy_link" target="_blank" rel="noopener noreferrer"><strong> {{ instituteDetails.name }}</strong></a>
                 Privacy Statement
              </label>
              <div v-if="privacyPolicyError" class="text-danger mt-1">
                {{ privacyPolicyError }}
              </div>
            </div> -->
                        <button type="submit" ref="submitButton" class="btn btn-lg btn-primary w-100"
                            :disabled="submitting">
                            <span v-if="!submitting">NEXT</span>
                            <span v-else class="spinner-border spinner-border-sm"></span>
                        </button>
                    </form>
                    <div class="text-center mt-10">
                        <label class="form-check-label" for="privacyPolicy">
                            Already have an account ?<router-link class="form-check-label"
                                :to="{ name: 'sign-in' }">
                                <strong> Sign In.</strong>
                            </router-link>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-lg-row-fluid w-lg-50 bgi-size-cover bgi-position-center order-1 order-lg-2"
            style="background-color: #000; color: #fff">
            <div class="d-flex flex-column flex-center px-10 pt-10 py-lg-15 px-md-15 w-100">
                <!-- Logo -->
                <div class="text-center">
                    <router-link to="/" class="">
                        <img alt="Logo" src="media/logos/Full_TCD_Logo_White.png" class="h-45px h-lg-60px" />
                    </router-link>
                </div>
                <img class="d-none d-lg-block mx-auto w-275px w-md-50 w-xl-500px mt-7" src="/media/misc/Login_page.gif"
                    alt="Auth Screen" />
                <div class="w-xl-600px">
                    <h1 class="d-lg-block text-white fs-lg-19 fw-semibold text-center my-7">
                        <!-- Welcome to The Careers Department. Are you ready to discover your
            future career? -->
                        In partnership with The University of Sydney you have access to this employability and careers
                        program.
                    </h1>
                    <div class="d-lg-block text-white fs-4 text-center">
                        <!-- Gain real-world insights and hear stories that bring career
            opportunities to life, helping you discover your possibilities. -->
                        If you want to know how to best leverage your degree in the workforce, this is the program for
                        you. ​
                        You can access careers profiling, psychometric testing, industry explainers, virtual work
                        experience modules and e-Portfolio tools. ​
                    </div>
                    <!-- <div class="d-lg-block text-white fs-4 text-center">
            You can access careers profiling, psychometric testing, industry explainers, virtual work experience modules and e-Portfolio tools. ​
          </div> -->
                </div>

                <a href="#" class="mt-8 my-md-7 mb-lg-0 mt-lg-20">
                    <div v-if="instituteDetails.logo" class="">
                        <img :src="instituteDetails.logo" alt="Logo" class="h-40px h-lg-80px" />
                    </div>
                </a>
            </div>
        </div>
    </div>
</template>
  <script lang="ts">
import { ref, onMounted, defineProps, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import axios from "axios";
import * as Yup from "yup";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import { useRegisterStore } from "@/stores/Auth/RegisterStore";
import { ValidationError } from "yup";

export default {
  setup() {
    const authStore = useRegisterStore();
    const route = useRoute();
    const router = useRouter();
    const slug = route.params.instituteSlug;
    const email = ref("");
    // const acceptedPrivacyPolicy = ref(false);
    const submitting = ref(false);
    const instituteDetails = ref({
      id: "",
      school_id: "",
      logo: "",
      name: "",
      privacy_link: "",
      show_postcode: true,
      school: {
        state_id: "",
        postcode: "",
        verified_domains: [],
      },
    });

    const submitButton = ref<HTMLButtonElement | null>(null);
    const emailError = ref<string | null>(null);
    // const privacyPolicyError = ref<string | null>(null);

    onMounted(() => {
      getInstituteDetails();
    });

    watch(email, (newVal, oldVal) => {
      if (newVal !== newVal.toLowerCase()) {
        email.value = newVal.toLowerCase();
      }
    });

    const emailSchema = Yup.object({
      email: Yup.string()
        .trim()
        .email("Invalid email")
        .required("Email is required"),
    });

    // const checkboxSchema = Yup.object({
    //   acceptedPrivacyPolicy: Yup.boolean().oneOf(
    //     [true],
    //     "You must accept the Privacy Policy."
    //   ),
    // });

    const handleSubmit = async () => {
      submitting.value = true;
      emailError.value = null;
      // privacyPolicyError.value = null;

      try {
        await emailSchema.validate(
          { email: email.value },
          { abortEarly: false }
        );

        const response = await axios.get("/api/checkEmail", {
          params: {
            email: email.value,
            institute_id: instituteDetails.value.id,
          },
        });

        // await checkboxSchema.validate(
        //   {
        //     acceptedPrivacyPolicy: acceptedPrivacyPolicy.value,
        //   },
        //   { abortEarly: false }
        // );

        authStore.email = email.value;
        authStore.underUniversity = true;
        authStore.isNew = true;  
        authStore.instituteDomain = (instituteDetails.value.school?.verified_domains.map((d: any) => d.domain).filter((domain: string | undefined) => domain) ?? []) as string[];
        authStore.studentDetail.school.id = instituteDetails.value.school_id;
        authStore.studentDetail.school.logo = instituteDetails.value.logo;
        authStore.studentDetail.school.name = instituteDetails.value.name;
        authStore.studentDetail.state = instituteDetails.value.school.state_id;
        authStore.showPostcode = instituteDetails.value.show_postcode;
        authStore.privacyLink = instituteDetails.value.privacy_link;

        if (instituteDetails.value.show_postcode === false) {
          authStore.studentDetail.postcode =
            instituteDetails.value.school.postcode;
        }

        router.push({
          name: "sign-up-institute-details",
        });
      } catch (err) {
        if (err instanceof ValidationError) {
          err.inner.forEach((validationError) => {
            if (validationError.path === "email") {
              emailError.value = validationError.message;
            }

            // if (validationError.path === "acceptedPrivacyPolicy") {
            //   privacyPolicyError.value = validationError.message;
            // }
          });
        }

        if (axios.isAxiosError(err)) {
          if (err.response) {
            emailError.value =
              err.response.data?.messages ||
              err.response.data?.errors?.email?.[0] ||
              "Something went wrong. Please try again.";
          } else {
            emailError.value = "Something went wrong. Please try again.";
          }
        }

        submitting.value = false;
      }
    };

    const getInstituteDetails = async () => {
      try {
        const response = await axios.get("/api/getInstituteDetail", {
          params: {
            slug: slug,
          },
        });
        instituteDetails.value = response.data; 
      } catch (error) {
        console.error("Failed to fetch institute details:", error);

        Swal.fire({
          html: "Institute not found",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Try again!",
          customClass: {
            confirmButton: "btn fw-semibold btn-light-dark",
          },
        });
      }
    };

    return {
      email,
      // acceptedPrivacyPolicy,
      submitting,
      handleSubmit,
      getInstituteDetails,
      instituteDetails,
      submitButton,
      emailError,
      // privacyPolicyError,
    };
  },
};
</script>
  
  <style scoped>
.bgi-size-cover {
  background-size: cover;
}
.bgi-position-center {
  background-position: center;
}
.text-white {
  color: #fff;
}

@media (min-width: 768px) {
  .bgc-md-white {
    background-color: #ffff !important;
  }
}
@media (min-width: 992px) {
  .fs-lg-19 {
    font-size: 1.9rem;
  }
}
</style>