<?php

namespace App\Services;

use App\Student;
use App\Employer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class EmployerPipelineService
{
    /**
     * Cache duration for pipeline data (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Get optimized pipeline students query for an employer
     * Uses UNION queries instead of OR conditions for better performance
     *
     * @param Employer $employer
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getPipelineStudentsQuery(Employer $employer)
    {
        $company = $this->getCompanyWithRelations($employer);
        
        if (!$company) {
            return Student::whereRaw('1 = 0'); // Return empty query if no company
        }

        // Get company-related IDs (cached for 5 minutes)
        $companyData = $this->getCachedCompanyData($company);
        
        // Build the main query using UNION for better performance
        $studentIds = $this->getUnionPipelineStudentIds($companyData);
        
        // Create the main query with the student IDs
        $query = Student::whereIn('id', $studentIds);
        
        // Apply request-based filters
        $this->applyRequestFilters($query, $employer);
        
        return $query;
    }

    /**
     * Get cached pipeline student count for dashboard
     *
     * @param Employer $employer
     * @return int
     */
    public function getCachedPipelineCount(Employer $employer): int
    {
        $cacheKey = "employer_pipeline_count_{$employer->id}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            return $this->getPipelineStudentsQuery($employer)->count();
        });
    }

    /**
     * Get cached top locations for pipeline students
     *
     * @param Employer $employer
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public function getCachedTopLocations(Employer $employer, int $limit = 3)
    {
        $cacheKey = "employer_pipeline_locations_{$employer->id}_{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer, $limit) {
            return $this->getPipelineStudentsQuery($employer)
                ->join('states', 'users.state_id', '=', 'states.id')
                ->selectRaw('states.name as state_name, states.code as state_code, COUNT(users.id) as student_count')
                ->groupBy('states.id', 'states.name', 'states.code')
                ->orderByDesc('student_count')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Get company with necessary relations (cached)
     *
     * @param Employer $employer
     * @return mixed
     */
    private function getCompanyWithRelations(Employer $employer)
    {
        $cacheKey = "employer_company_relations_{$employer->company_id}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            return $employer->company()->with([
                'industries',
                'industryunits',
                'workexperienceTemplates',
                'skillstrainingTemplates',
                'lessons'
            ])->first();
        });
    }

    /**
     * Get cached company data IDs
     *
     * @param $company
     * @return array
     */
    private function getCachedCompanyData($company): array
    {
        $cacheKey = "company_data_ids_{$company->id}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($company) {
            return [
                'industry_ids' => $company->industries->pluck('id')->toArray(),
                'industryunit_ids' => $company->industryunits->pluck('id')->toArray(),
                'workexperience_template_ids' => $company->workexperienceTemplates->pluck('id')->toArray(),
                'skillstraining_template_ids' => $company->skillstrainingTemplates->pluck('id')->toArray(),
                'lesson_ids' => $company->lessons->pluck('id')->toArray(),
            ];
        });
    }

    /**
     * Get student IDs using optimized UNION queries
     *
     * @param array $companyData
     * @return array
     */
    private function getUnionPipelineStudentIds(array $companyData): array
    {
        $queries = [];

        // Students who submitted workexperience responses
        if (!empty($companyData['workexperience_template_ids'])) {
            $queries[] = DB::table('workexperience_responses as wr')
                ->select('wr.student_id as id')
                ->whereIn('wr.template_id', $companyData['workexperience_template_ids'])
                ->where('wr.status', 'Submitted');
        }

        // Students who submitted skillstraining responses
        if (!empty($companyData['skillstraining_template_ids'])) {
            $queries[] = DB::table('skillstraining_responses as sr')
                ->select('sr.student_id as id')
                ->whereIn('sr.template_id', $companyData['skillstraining_template_ids'])
                ->where('sr.status', 'Submitted');
        }

        // Students who submitted lesson responses
        if (!empty($companyData['lesson_ids'])) {
            $queries[] = DB::table('lessonresponses as lr')
                ->select('lr.student_id as id')
                ->whereIn('lr.lesson_id', $companyData['lesson_ids'])
                ->where('lr.status', 'Submitted');
        }

        // Students with matching gameplan industries
        if (!empty($companyData['industry_ids'])) {
            $queries[] = DB::table('gameplans as g')
                ->select('g.user_id as id')
                ->join('gameplan_industries as gi', 'g.id', '=', 'gi.gameplan_id')
                ->whereIn('gi.industry_category_id', $companyData['industry_ids'])
                ->where('g.is_latest', true);
        }

        // Students who favorited company industryunits
        if (!empty($companyData['industryunit_ids'])) {
            $queries[] = DB::table('favorites as f')
                ->select('f.user_id as id')
                ->where('f.favoriteable_type', 'App\Industryunit')
                ->whereIn('f.favoriteable_id', $companyData['industryunit_ids']);
        }

        if (empty($queries)) {
            return [];
        }

        // Combine all queries with UNION and get distinct student IDs
        $unionQuery = $queries[0];
        for ($i = 1; $i < count($queries); $i++) {
            $unionQuery = $unionQuery->union($queries[$i]);
        }

        return DB::table(DB::raw("({$unionQuery->toSql()}) as pipeline_students"))
            ->mergeBindings($unionQuery)
            ->distinct()
            ->pluck('id')
            ->toArray();
    }

    /**
     * Apply request-based filters to the query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Employer $employer
     */
    private function applyRequestFilters($query, Employer $employer)
    {
        // Search filter
        $search = request('search');
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // State filter
        $state = request('state');
        if ($state) {
            $query->whereHas('state', function ($q) use ($state) {
                $q->where('code', $state);
            });
        }

        // Following filter
        $isFollowing = request('is_following');
        if ($isFollowing) {
            $followedStudentIds = $this->getCachedFollowedStudents($employer);
            $query->whereIn('id', $followedStudentIds);
        }

        // Additional filters for completed modules, engaged content, etc.
        $this->applyAdvancedFilters($query, $employer);
    }

    /**
     * Get cached followed student IDs
     *
     * @param Employer $employer
     * @return array
     */
    private function getCachedFollowedStudents(Employer $employer): array
    {
        $cacheKey = "employer_followed_students_{$employer->id}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($employer) {
            return $employer->followedStudents()->pluck('following')->toArray();
        });
    }

    /**
     * Apply advanced filters based on request parameters
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Employer $employer
     */
    private function applyAdvancedFilters($query, Employer $employer)
    {
        $completedModule = request('completed_module');
        $engagedContent = request('engaged_content');
        $industriesInGameplan = request('industries_in_gameplan');
        $companiesInGameplan = request('companies_in_gameplan');

        if (!($completedModule || $engagedContent || $industriesInGameplan || $companiesInGameplan)) {
            return;
        }

        $company = $this->getCompanyWithRelations($employer);
        $companyData = $this->getCachedCompanyData($company);

        $query->where(function ($q) use ($completedModule, $engagedContent, $industriesInGameplan, $companiesInGameplan, $companyData, $company) {
            if ($completedModule) {
                $q->where(function ($subQuery) {
                    $subQuery->whereHas('workexperienceResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('skillstrainingResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('lessonresponses', function ($q) {
                        $q->where('status', 'Submitted');
                    });
                });
            }

            if ($engagedContent) {
                $q->orWhereHas('favorites', function ($subQ) {
                    $subQ->where('favoriteable_type', 'App\Industryunit');
                });
            }

            if ($industriesInGameplan && !empty($companyData['industry_ids'])) {
                $q->orWhereHas('latestGameplan', function ($subQ) use ($companyData) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyData) {
                        $industryQ->whereIn('industry_categories.id', $companyData['industry_ids']);
                    });
                });
            }

            if ($companiesInGameplan && $company && $company->detail) {
                $q->orWhereHas('latestGameplan', function ($subQ) use ($company) {
                    $subQ->whereHas('companies', function ($companyQ) use ($company) {
                        $companyQ->where('company_name', 'LIKE', "%{$company->detail->name}%");
                    });
                });
            }
        });
    }

    /**
     * Clear cache for an employer's pipeline data
     *
     * @param Employer $employer
     */
    public function clearPipelineCache(Employer $employer)
    {
        $patterns = [
            "employer_pipeline_count_{$employer->id}",
            "employer_pipeline_locations_{$employer->id}_*",
            "employer_company_relations_{$employer->company_id}",
            "company_data_ids_{$employer->company_id}",
            "employer_followed_students_{$employer->id}",
        ];

        foreach ($patterns as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // For wildcard patterns, we'd need Redis SCAN or similar
                // For now, just clear the specific keys we know about
                for ($i = 1; $i <= 10; $i++) {
                    Cache::forget(str_replace('*', $i, $pattern));
                }
            } else {
                Cache::forget($pattern);
            }
        }
    }
}
