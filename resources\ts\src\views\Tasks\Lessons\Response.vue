<template>
    <div id="banner" class="full-view-banner banner" v-bind:style="{
        backgroundImage: 'url(' + lesson.background_imagepath + ')',
    }">
        <div v-if="lesson.background_video" class="banner-video" v-html="lesson.background_video"></div>
        <div style="
                position: absolute;
                width: 100%;
                height: 100%;
                opacity: 0.3;
                background: #000;
            "></div>
        <div class="banner_detail_box w-450px">
            <div v-if="lesson.badge  && lesson.compeletedpercent !== 100" class="mt-4 mb-4">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center mb-10">
                            <img :src="lesson.badge?.image_fullpath" :alt="lesson.badge.name" class="me-3" width="25" />
                            <div>
                                <p class="mb-1 fw-normal text-light fs-4">
                                    {{ lesson.badge.name }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <h1 class="fw-normal text-light">Lesson</h1>
            <h1 class="display-4 fw-normal mb-4 text-light" v-html="lesson.title"></h1>
            <div class="row text-light align-items-center">
                <div class="col-md-4 col-lg-3" v-if="
                    lesson.estimated_time &&
                    (lesson.estimated_time.hours ||
                        lesson.estimated_time.minutes)
                ">
                    <i class="fa-regular fa-clock text-white me-2"></i>
                    <span v-if="
                        lesson.estimated_time && lesson.estimated_time.hours
                    " v-text="lesson.estimated_time.hours + 'h '"></span>
                    <span v-if="
                        lesson.estimated_time &&
                        lesson.estimated_time.minutes
                    " v-text="lesson.estimated_time.minutes + 'm'"></span>
                </div>
                <div class="col-md-4 col-lg-3" v-if="lesson.level">
                    <i class="fa fa-chart-simple text-white me-2"></i>
                    <span v-text="lesson.level"></span>
                </div>
                <div class="col-md-5 col-lg-5 mt-lg-0 mt-md-3">

                    <!-- <span v-if="
                        lesson.compeletedpercent > 0 &&
                        lesson.compeletedpercent < 100
                    " class="text-dark px-5 py-2 rounded-pill w-full" style="background-color: #e9ff1f">{{ lesson.compeletedpercent }}% Completed</span>

                    <span v-else-if="lesson.compeletedpercent == 100" class="text-light px-5 py-2 rounded-pill w-full" style="background-color: #0062ff">Completed</span> -->
                    <ScormResultStatusBadge
                        :status="scormResult?.lesson_status"
                        :completion-percentage="lesson.compeletedpercent"
                    />
                </div>
            </div>
            <AnzscoDetails :tags-grouped="anzscoTagsGrouped" />
            <div class="row mt-5">
                <div class="col-sm-6 fs-6 text-light p-2" v-for="tag in lesson.tagged" :key="tag.id">
                    <i class="fa fa-check text-white"></i> {{ tag.tag_name }}
                </div>
            </div>
            <div class="row mt-5" v-if="lesson.foreground_video && lesson.compeletedpercent === 0">
                <div class="col-8 col-sm-6 col-md-10">
                    <button type="button" class="btn btn-black-custom btn-lg rounded-0 w-100 p-md-5" data-bs-toggle="modal" data-bs-target="#kt_modal_trailer">
                        Watch Trailer
                    </button>
                </div>
            </div>
            <div class="row mt-5" v-if="
                lesson.user_response &&
                lesson.user_response.status == 'Submitted'
            ">
                <div class="col-8 col-sm-6 col-md-10">
                    <button class="btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5" style="font-size: 14px !important" @click="viewResponse" v-if="lesson.user_response.response_path === ''">
                        View Response
                    </button>

                    <button v-else class="btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5" style="font-size: 14px !important" data-bs-toggle="modal" data-bs-target="#kt_modal_viewResponse">
                        View Response
                    </button>
                </div>
                <div class="col-sm-6 col-md-2 text-center my-auto" v-if="lesson.hasresponse">
                    <div v-if="lesson.compeletedpercent >= 100">
                        <p class="cursor-pointer fs-5 text-light d-flex gap-1 my-auto" data-bs-toggle="modal" data-bs-target="#kt_modal_reset_responses">
                            <i class="fa-solid fa-rotate-right fs-5 text-light my-auto "></i> Reset
                        </p>
                    </div>
                </div>
            </div>
            <div class="row my-5" v-if="lesson.hasresponse">
                <div class="col-8 col-sm-6 col-md-10 text-center">
                    <router-link class="p-5 text-light" style="font-size: 12px !important" :to="{
                        name: 'task-lessons-section-detail',
                        params: { id: currentlesson, sectionid: 1 },
                    }">
                        Edit Response
                    </router-link>
                </div>
            </div>


            <div class="row row-cols-3">

                <div v-if="lesson.hasresponse && lesson.user_response.badge_key && lesson.compeletedpercent === 100 && scormModulePassed" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" @click="openBadgeModal(lesson.user_response.badge_key)">
                                <img :src="lesson.badge?.image_fullpath" :alt="lesson.badge.name" class="me-3" width="25" />
                                <div class="overflow-hidden">
                                    <p class="fw-bold text-light my-auto">
                                        View Badge
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="lesson.feedback" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer w-fit-content " data-bs-toggle="modal" data-bs-target="#kt_modal_feedback">
                                <i class="fa-solid fa-comments text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Feedback
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="lesson.compeletedpercent === 100 && scormResult?.lesson_status" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer w-fit-content" @click="openScormModal">
                                <i class="fa-solid fa-clipboard text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Results
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- <div v-if="lesson.response && lesson.user_response.response_path">
                    <div class="mt-4 mb-4 cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_viewFile" @click="loadFile">
                        <div class="fw-bold">
                            <div>
                                <i class="fa-solid fa-file text-light"></i> <span class="mx-1"> View File </span>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>

        </div>
    </div>
    <div :class="{
        row: lesson.user_response.activity_responses.length < 6,
        'sticky-top': scrolled,
    }" v-on="handleScroll" class="d-flex bg-black module-sections">
        <template v-for="step in lesson.user_response.activity_responses" :key="step.activity.id">
            <div class="text-center p-0" v-bind:class="[
                lesson.user_response.activity_responses.length < 6
                    ? 'col'
                    : 'col-6 col-sm-4 col-md-2',
                'bg-black',
            ]">
                <div class="module-section d-flex flex-column justify-content-center align-items-center py-5">
                    <span class="svg-icon svg-icon-primary svg-icon-2x">
                        <svg v-if="step" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <mask fill="white">
                                    <use xlink:href="#path-1" />
                                </mask>
                                <g />
                                <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" />
                            </g>
                        </svg>
                        <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <mask fill="white">
                                    <use xlink:href="#path-1" />
                                </mask>
                                <g />
                                <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" />
                            </g>
                        </svg>
                    </span>
                    <p class="m-0 px-5 text-white" v-html="step.activity.title"></p>
                    <p class="m-0 text-white">
                        <span v-if="
                            step.activity.estimated_time &&
                            step.activity.estimated_time.hours
                        " v-text="step.activity.estimated_time.hours + 'h '"></span>
                        <span v-if="
                            step.activity.estimated_time &&
                            step.activity.estimated_time.minutes
                        " v-text="step.activity.estimated_time.minutes + 'm'"></span>
                        &nbsp;
                    </p>
                </div>
            </div>
        </template>
        <div class="text-center p-0" v-bind:class="[
            lesson.user_response.activity_responses.length < 6
                ? 'col'
                : 'col-6 col-sm-4 col-md-2 ',
            'bg-black',
        ]">
            <div class="module-section d-flex flex-column justify-content-center align-items-center py-5">
                <span class="svg-icon svg-icon-primary svg-icon-2x">
                    <svg v-if="lesson.user_response" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <mask fill="white">
                                <use xlink:href="#path-1" />
                            </mask>
                            <g />
                            <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" />
                        </g>
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <mask fill="white">
                                <use xlink:href="#path-1" />
                            </mask>
                            <g />
                            <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" />
                        </g>
                    </svg>
                </span>
                <p class="m-0" :class="{ 'text-white': lesson.user_response }">
                    Final Step
                </p>
                <p class="m-0" :class="{ 'text-white': lesson.user_response }">
                    &nbsp;
                </p>
            </div>
        </div>
    </div>
    <div id="lessonSections" class="section-content">

        <div class="d-flex justify-content-center">
            <div class="container">
                <div class="row border rounded p-10 d-flex" style="min-height: 70vh;">
                    <div class=" col-lg-3 col-md-8 overflow-auto" style="max-height: 70vh;">
                        <ul class="nav nav-tabs nav-pills flex-row border-0 flex-md-column me-5 mb-3 mb-md-0 fs-6">
                            <template v-for="step in lesson.user_response.activity_responses" :key="step.activity.id">
                                <li class="nav-item w-100 me-0 mb-md-2">
                                    <a class="nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark" :class="{ 'active': selectedActivityId === step.activity.id }" @click="selectedActivityId = step.activity.id">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Section {{ step.activity.number }}</span>
                                            <!-- <span class="fs-7 text-left" v-html="step.activity.title"></span> -->
                                            <span class="fs-7 text-left text-capitalize">{{ step.activity.title?.toLowerCase() }}</span>

                                        </span>
                                    </a>
                                </li>
                                <!-- {{ step }} -->
                            </template>
                            <li class="nav-item w-100 me-0 mb-md-2">
                                <a class="nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark" :class="{ 'active': selectedActivityId === null }" @click="selectedActivityId = null">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Final Step</span>
                                            <span class="fs-7">Submission</span>
                                        </span>
                                    </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col overflow-auto" style="max-height: 70vh;">

                        <template v-for="step in lesson.user_response.activity_responses" :key="step.activity.id">

                            <div v-if="selectedActivityId === step.activity.id">

                                <router-link :to="{
                                    name: 'task-lessons-section-detail',
                                    params: { id: currentlesson, sectionid: step.activity.number },
                                }"
                                custom
                                v-slot="{ href }"
                                >
                                <a
                                    :href="href"
                                    target="_blank"
                                    rel="noopener"
                                    class="d-flex justify-content-end me-3 position-sticky top-0 bg-white p-2 gap-1"
                                >
                                    <i class="fa fa-eye my-auto"></i> View Module
                                </a>
                                </router-link>

                                <div v-if="
                                    (step.activity.estimated_time &&
                                        step.activity.estimated_time.hours) ||
                                    (step.activity.estimated_time &&
                                        step.activity.estimated_time.minutes)
                                ">
                                    <i class="fa-regular fa-clock text-dark me-2"></i>
                                    <span v-if="
                                        step.activity.estimated_time &&
                                        step.activity.estimated_time.hours
                                    " v-text="step.activity.estimated_time.hours + 'h '"></span>
                                    <span v-if="
                                        step.activity.estimated_time &&
                                        step.activity.estimated_time.minutes
                                    " v-text="step.activity.estimated_time.minutes + 'm'"></span>
                                </div>

                                <!-- <div class="my-5" v-html="step.activity.body"></div> -->

                                <div v-if="step.activity.response && step.response" v-html="step.response" class="froala-response mb-5" ></div>
                                <div v-if="!step.activity.response && !step.activity.is_scorm" class="my-2">
                                    <h3>
                                        <i class="far fa-times-circle"></i> No answer required on this section.
                                    </h3>
                                </div>
                                <SectionScormResponse
                                    v-if="step.activity.is_scorm"
                                    :sections-map="sectionsMap"
                                    :current-section-id="step.activity.id"
                                />
                            </div>
                            <div v-if="!selectedActivityId === step.activity.id">
                                    <span class="text-dark">
                                        <i class="fa-regular fa-circle-xmark text-dark"></i>
                                        <span class=""> No was answer required on this section. </span>
                                    </span>
                            </div>
                        </template>

                        <div v-if="selectedActivityId === null" class="text-center mt-5">

                            <div v-if="lesson.user_response.filename && lesson.user_response.filename.trim() !== ''" >

                                <h4>Students were asked to upload a document on their final step.</h4>
                                <div class="d-flex justify-content-center gap-10 pt-10">

                                    <button class="btn btn-secondary rounded" style="font-size: 14px !important" data-bs-toggle="modal" data-bs-target="#kt_modal_viewResponse">
                                        <i class="fa fa-eye"></i>  View Response
                                    </button>

                                    <div v-if="lesson.response && lesson.user_response.response_path" >
                                         <a :href="'/tasks/' + lesson.user_response.id +  '/download-response'" class="btn btn-secondary rounded"><i class="fa fa-download"></i> Download Response</a>
                                    </div>
                                </div>

                            </div>
                            <div v-else class="text-center">
                            <h4>Students were not asked to upload a document on the final step of this module. Once submitted, the module is complete.</h4>
                                <div v-if="lesson.compeletedpercent == 100" class="mt-7">
                                    <i class="fa fa-check-circle text-success"></i> Submitted
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="row" v-if="lesson.response && lesson.user_response.response_path">
            <div class="col-12 col-md-8 offset-md-2">
                <h1 class="fw-normal mb-4 text-dark">Uploaded file:</h1>
                <div class="my-5" v-text="lesson.user_response.filename"></div>
                <div class="my-5">
                    <a :href="'/tasks/' +
                        lesson.user_response.id +
                        '/download-response'
                        " class="btn btn-primary rounded-0">View</a>
                </div>
            </div>
        </div> -->

        <!-- <div class="row mt-3" v-if="lesson.user_response && lesson.user_response.feedback">
            <div class="bg-light teacher-feedback col-12 col-md-8 offset-md-2">
                <h1 class="fw-normal mb-4 text-dark">Feedback</h1>
                <div class="my-5" v-html="lesson.user_response.feedback"></div>
            </div>
        </div> -->
    </div>
    <div class="modal fade" id="kt_modal_trailer" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-900px">
            <div class="modal-content rounded-0">
                <div class="modal-body bg-black p-1" v-html="lesson.foreground_video"></div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="kt_modal_reset_responses" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <p>
                        Do you really want to reset your response? Doing this
                        will clear your answers and also any feedback that has
                        been provided.
                    </p>
                    <button type="button" class="btn btn-primary btn-sm rounded-0" data-bs-dismiss="modal" @click="resetLesson(lesson.id)">
                        Yes
                    </button>
                    <button type="button" class="btn btn-sm btn-primary rounded-0 m-5" data-bs-dismiss="modal">
                        No
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="kt_modal_feedback" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-600px">
            <div class="modal-content rounded-0" style="height: 80vh">
                <div class="modal-header text-white">
                    <h5 class="modal-title">Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 bg-gray-50 text-left">
                    <div class="p-4 bg-white" style="height: 90%">
                        <p v-html="lesson.user_response.feedback" class="text-gray-700"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <ScormResultModal
        ref="scormModalRef"
        v-if="scormResult?.lesson_status"
        :module="lesson"
        trackable-type="lessonsteps"
        :redo-success-route="{
            name: `task-lessons-section-detail`,
            params: {
                id: lesson.scorm_scoring_step_result?.lesson_id,
                sectionid: lesson.scorm_scoring_step_result?.number,
            },
        }"
        @viewBagdeDetails="openBadgeModal(lesson.user_response.badge_key)"
    />

    <BadgeModal :selectedBadge="selectedBadge" :module-data="lesson"  @shareBadge="openShareBadgeModal" />

    <ResponseModal :modalSrc="modalSrc" :downloadUrl="downloadUrl" />

</template>
<script lang="ts">
import {
    defineComponent,
    ref,
    onMounted,
    nextTick,
    watch,
    watchEffect,
    computed,
} from "vue";
import ApiService from "@/core/services/ApiService";
import VueFroala from "froala-wysiwyg-vue3";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import iframeResize from "iframe-resizer/js/iframeResizer";
import BadgeModal from "../Badges/BadgeModal.vue";
import ResponseModal from "@/components/tasks/ResponseModal.vue";
import ScormResultModal from "@/components/modules/response/ScormResultModal.vue";
import AnzscoDetails from "@/components/modules/detail/AnzscoDetails.vue";
import ScormResultStatusBadge from "@/components/modules/response/ScormResultStatusBadge.vue";
import SectionScormResponse from "@/components/modules/response/SectionScormResponse.vue";

// Import Bootstrap
import * as bootstrap from 'bootstrap';

interface Badge {
    id: number;
    name: string;
    description?: string;
    image_fullpath: string;
    formatted_issue_date: string;
    formatted_expiration_date: string;
    video?: string | null;
}


export default defineComponent({
    name: "lessons-detail",
    components: {
        VueFroala,
        BadgeModal,
        ResponseModal,
        // SCORM AND ANZSCO - START
        ScormResultModal,
        AnzscoDetails,
        ScormResultStatusBadge,
        SectionScormResponse,
        // SCORM AND ANZSCO - END
    },
    setup() {
        const store = useStore();
        const route = useRoute();
        const selectedBadge = ref<Badge | object>({});
        const showModal = ref(false);
        const excelUrl = ref<string | null>(null);
        const currentUser = store.getters.currentUser;
        const modalSrc = ref<string>('');
        const modalId = ref('')
        const downloadUrl = ref<string>('');


        onMounted(async () => {
            await fetchLessonDetail();
            iframeResize(
                {
                    heightCalculationMethod: "bodyScroll",
                },
                ".section-content iframe"
                // document.querySelectorAll(".section-content iframe")
            );
        });


        onMounted(() => {
            const modalEl = document.getElementById('kt_modal_viewResponse')

            if (!modalEl) {
                console.warn('Modal element not found: #kt_modal_viewResponse')
                return
            }

            modalEl.addEventListener('show.bs.modal', (event: any) => {
                const button = event.relatedTarget as HTMLElement
                if (button) {
                    modalSrc.value = lesson.value.user_response.view_response_file_path;

                    modalId.value = lesson.value.user_response.id;

                    downloadUrl.value = `/tasks/${modalId.value}/download-response`;
                }
            })
        })

        const lesson = ref();
        const currentlesson = ref();
        const setLesson = ref();
        const scrolled = ref(false);
        let sectionNavOffsetTop = 0;

        lesson.value = {
            id: 1,
            background_imagepath: null,
            background_video: null,
            user_response: {
                id: null,
                filename: "",
                response_path: "",
                activity_responses: {},
                badge_key : {}
            },
            scorm_scoring_step_result: {
                user_scorm_result: {}
            },
            compeletedpercent: null,
        };
        currentlesson.value = route.params.id;

        const viewResponse = () => {
            // document.querySelector('.module-section') !.scrollIntoView({ behavior: 'smooth' });
            var banner = document.querySelector(".banner");
            window.scrollBy({
                top: banner!.scrollHeight, // could be negative value
                left: 0,
                behavior: "smooth",
            });
        };

        const fetchLessonDetail = async () => {
            try {
                const { data } = await ApiService.get(
                    `api/lessons`,
                    currentlesson.value
                );

                lesson.value = data;
                lesson.value.user_response.activity_responses.sort(
                    (a, b) => a.activity.number - b.activity.number
                );

                var banner = document.getElementById("banner");
                sectionNavOffsetTop = banner!.scrollHeight + 120;

                if (data.user_response?.response_path === "") {
                    waitForSectionAndScroll();
                }

            } catch (error) {
                console.log(error);
            }
        };

        const handleScroll = () => {
            var windowWidth =
                window.innerWidth ||
                document.documentElement.clientWidth ||
                document.body.clientWidth;
            if (windowWidth > 991) {
                var element = document.getElementById(
                    "kt_app_toolbar"
                ) as HTMLElement;
                if (window.scrollY > sectionNavOffsetTop) {
                    scrolled.value = true;
                    element.style.display = "none";
                } else {
                    scrolled.value = false;
                    element.style.display = "flex";
                }
            }
        };
        const resetLesson = (id) => {
            setLesson.value = {
                id: id,
            };
            ApiService.post(`api/lessons/` + id + `/reset`, setLesson.value)
                .then(({ data }) => {
                    Swal.fire({
                        text: "This lesson and your previous responses have been reset.",
                        icon: "success",
                        buttonsStyling: false,
                        confirmButtonText: "Ok",
                        customClass: {
                            confirmButton:
                                "btn fw-semobold btn-light-primary rounded-0",
                        },
                    }).then(() => {
                        // window.location.reload();
                        window.location.replace("#/tasks/lessons/" + id);
                    });
                    // lesson.value.favourite = data.favourite;
                })
                .catch(({ response }) => { });
        };

        const openBadgeModal = (badge: Badge) => {
            selectedBadge.value = badge;
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const openShareBadgeModal = (badge: Badge) => {
            selectedBadge.value = badge;
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_share_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const filePreview = ref<{ url: string; type: string } | null>(null);
        const fileType = ref<string | null>(null);
        const excelData = ref([]);
        const previewUrl = ref<string>('');

        const waitForSectionAndScroll = (retries = 10) => {
           const section = document.getElementById("lessonSections");
            if (section) {
                section.scrollIntoView({ behavior: "smooth", block: "start" });
                console.log("Scrolled to #lessonSections");
            } else if (retries > 0) {
                setTimeout(() => waitForSectionAndScroll(retries - 1), 300); // Retry every 300ms
            } else {
                console.log("#lessonSections not found after retries");
            }
        };


        const selectedActivityId = ref(null);

        // SCORM AND ANZSCO -START
        const anzscoTagsGrouped = computed(() => {
            return lesson.value.anzsco_tag_names_grouped;
        });
        const scormModalRef = ref();
        const openScormModal = () => {
            scormModalRef.value?.openModal();
        }
        const scormResult = computed(() => {
            return lesson.value?.scorm_scoring_step_result?.user_scorm_result;
        });
        const sectionsMap = computed(() => {
            return new Map<number, any>(lesson.value.steps.map(step => [step.id, step]))
        });
        const scormModulePassed = computed(() => {
            if (scormResult.value?.lesson_status) { // If scorm scoring step exists with its result
                return !['failed','incomplete'].includes(scormResult.value.lesson_status);
            }

            return true;
        });
        // SCORM AND ANZSCO - END

        return {
            currentUser,
            lesson,
            currentlesson,
            config: {
                key: "hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",
                height: 300,
                attribution: false,
                toolbarButtons: [""],
                events: {
                    initialized: function () {

                    },
                },
            },
            scrolled,
            handleScroll,
            resetLesson,
            viewResponse,
            selectedBadge,
            openBadgeModal,
            openShareBadgeModal,
            showModal,
            previewUrl,
            fileType,
            filePreview,
            excelData,
            downloadUrl,
            selectedActivityId,
            waitForSectionAndScroll,
            modalSrc,
            // SCORM AND ANZSCO - START
            anzscoTagsGrouped,
            scormModalRef,
            openScormModal,
            scormResult,
            sectionsMap,
            scormModulePassed,
            // SCORM AND ANZSCO - END
        };
    },
    props: ["id"],
    created() {
        window.addEventListener("scroll", this.handleScroll);
    },
    destroyed() {
        window.removeEventListener("scroll", this.handleScroll);
    },
});
</script>

<style>

.wrap {
    overflow: hidden;
    max-width: 75ch;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.btn-white-custom {
    background: #fff;
    color: #000;
}

.btn-black-custom:hover,
.btn-white-custom {
    background-color: #fff !important;
    color: #000 !important;
}

.btn-black-custom,
.btn-white-custom:hover {
    background-color: #000 !important;
    color: #fff !important;
}

.btn-white-custom:hover,
.btn.btn-white-custom:hover:not(.btn-active) {
    background-color: #000 !important;
    color: #fff !important;
}

.module-sections {
    overflow: auto hidden;
    margin-left: -30px;
    margin-right: -30px;
    position: relative;
}

.sticky-top {
    position: fixed;
    min-width: calc(100% - 140px);
}

.module-section {
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-left: 1px solid;
    cursor: pointer;
    height: 100px;
}

.module-sections>.text-center:last-of-type>.module-section {
    border-right: 1px solid;
}

.app-content {
    padding: 0px;
}

.banner_detail_box {
    position: absolute;
    top: 50%;
    left: 20%;
    transform: translate(-50%, -50%);
}

.modal-backdrop {
    opacity: 0.8 !important;
}

/* .sticky-top+.section-content {
                                margin-top: 150px;
                            } */

.section-content {
    margin-top: 50px;
    padding-bottom: 50px;
}

.section-content iframe {
    width: 100% !important;
}

.section-content iframe.wistia_embed {
    height: 100% !important;
}

.section-content img {
    max-width: 100%;
}

.section-content p img,
.section-content p iframe {
    margin-bottom: -1rem;
}

.pointer {
    cursor: pointer;
}

.overlay {
    overflow: overlay;
}

.related {
    right: 5% !important;
}

.banner {
    background-color: #000;
    background-image: url("/images/vwe/home-parallax.jpg");
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    overflow: hidden;
    min-height: calc(56.25vw - 149px);
}

.full-view-banner {
    margin-left: -30px;
    margin-right: -30px;
}

.banner-video {
    height: 100%;
}

.banner-video>video {
    /* height: 100%; */
    width: 101% !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.froala-response,
.teacher-feedback {
    height: 300px;
    overflow: auto;
    padding: 20px;
    border-radius: 10px;
}

.froala-response {
    background-color: #fff;
    border: 1px solid #bbb;
}

.froala-response iframe {
    width: 100%;
}

.froala-response img {
    max-width: 100%;
}

div#kt_app_content {
    padding-top: 0px;
    padding-bottom: 0px;
}

@media (max-width: 1280px) {
    .banner {
        /* height: calc(56.25vw - 140px); */
        height: 56.25vw;
    }

    .banner_detail_box {
        left: 40%;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(65vw + 65vh) !important;
    }
}

@media (min-width: 992px) {

    .sticky-top+.section-content {
        margin-top: 100px;
    }

    .module-sections {
        animation-name: backtooriginal;
        animation-duration: 0.2s;
        animation-fill-mode: forwards;
        z-index: 100;
    }

    .sticky-top {
        animation-name: stick-top;
        animation-duration: 0.2s;
        animation-fill-mode: forwards;
    }

    @keyframes stick-top {
        from {
            top: 5px;
        }

        100% {
            top: 0px;
        }
    }

    @keyframes backtooriginal {
        from {
            top: -5px;
        }

        100% {
            top: 0px;
        }
    }
}

@media (max-width: 991px) {

    .full-view-banner,
    .module-sections {
        margin-left: -20px;
        margin-right: -20px;
    }

    .full-view-banner {
        margin-top: 58.16px;
    }

    .sticky-top {
        top: 119px;
        min-width: 100%;
    }

    .module-section {
        height: 100px;
    }
}

@media (max-width: 991px) and (min-width: 768px) and (orientation: portrait) {
    .banner {
        height: 86.25vw;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(66vw + 66vh) !important;
    }
}

@media (max-width: 991px) and (orientation: landscape) {
    .banner-video>video {
        height: auto !important;
        width: calc(70vw + 70vh) !important;
    }
}

@media (max-width: 767px) {
    .banner {
        height: calc(100vh - 300px);
    }

    .banner_detail_box {
        left: 50%;
    }

    .sticky-top {
        margin-top: 10px;
    }
}

@media (max-width: 575px) {
    div#kt_app_content {
        padding-top: 30px;
    }

    .banner_detail_box {
        width: 70vw !important;
    }

    .full-view-banner {
        margin-top: 0;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(90vw + 90vh) !important;
    }
}
</style>