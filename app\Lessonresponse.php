<?php

namespace App;

use App\Services\TimelineService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use App\Traits\GeneratesBadgeKey;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Lessonresponse extends Model
{
    use GeneratesBadgeKey;

    protected $guarded = [];
    protected $appends = ['response_file_s3_path', 'view_response_file_path'];


    protected static function booted()
    {

        static::deleting(function ($response) {
            if ($response->response_path) {
                Storage::cloud()->delete($response->response_path);
            }

            if ($response->activityResponses) {
                $response->activityResponses()->delete();
            }

            if ($response->activityLog) {
                $response->activityLog()->delete();
            }

            if ($response->badgeKey) {
                $response->badgeKey->delete();
            }
        });

        static::saved(function ($response) {
            $response->load('lesson');

            if ($response->status === 'Submitted' && $response->lesson->badge_id) {

                $scormStatus = $response->lesson->userScormModuleResult?->lesson_status;

                if ($scormStatus && in_array($scormStatus, ['failed', 'incomplete'])) {
                    // delete badge assigned if exists
                    if ($response->badgeKey) {
                        $response->badgeKey->delete();
                    }
                    return;
                }

                if (!$response->badgeKey) {
                    // Generate a new unique badge key
                    $badgeKey = self::generateBadgeKey();
                    $response->badgeKey()->create(['badge_id' => $response->lesson->badge_id, 'credential_id' => $badgeKey]);
                }
            }
        });

        static::created(function ($response) {
            (new TimelineService)->log(
                $response, // model
                'created', // event
            );
        });
        static::updated(function ($response) {
            (new TimelineService)->log(
                $response, // model
                'updated', // event
            );
        });
    }

    public function student()
    {
        return $this->belongsTo(User::class, 'student_id')->whereIn('role_id', [3, 4]);
    }

    public function year()
    {
        return $this->belongsTo(Standard::class, 'standard_id');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id')->whereIn('role_id', [1, 2]);
    }

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    public function activityResponses()
    {
        return $this->hasMany(TaskActivityResponse::class, 'lessonresponse_id');
    }

    public function getUserAndTaskAttribute()
    {
        $extention = pathinfo($this->response_path, PATHINFO_EXTENSION);
        $file = mb_ereg_replace("([^\w\s\d\-_~,;\[\]\(\).])", '', $this->student->name . '-' . $this->lesson->title);
        $file = mb_ereg_replace("([\.]{2,})", '', $file);
        $file = str_replace(" ", '_', $file);
        $filename = $file . '.' . $extention;
        return $filename;
    }

    public function getSubmittedAtAttribute($value)
    {
        return Carbon::parse($value)->format('M d, Y g:i a');
    }

    public function scopeSubmitted($query)
    {
        return $query->whereStatus('Submitted');
    }

    public function activityLog()
    {
        return $this->morphOne(ActivityLog::class, 'subject');
    }

    protected function responseFileS3Path(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->response_path ? Storage::url($this->response_path): null
        );
    }

    protected function viewResponseFilePath(): Attribute
    {
        return Attribute::make(
            get: fn() => match (pathinfo($this->response_path, PATHINFO_EXTENSION)) {
                'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx' => 'https://view.officeapps.live.com/op/view.aspx?src=' . urlencode($this->response_file_s3_path),
                'pdf' => $this->response_file_s3_path,
                default => 'unsupported',
            }
        );
    }


    public function badgeKey()
    {
        return $this->morphOne(BadgeKey::class, 'badgeable');
    }

}
