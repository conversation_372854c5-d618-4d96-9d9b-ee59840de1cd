<template>
    <div id="banner" class="full-view-banner banner" v-bind:style="{ 'backgroundImage': 'url(' + vwe.background_imagepath + ')' }">
        <div v-if="vwe.background_videoid" class="banner-video" v-html="vwe.background_videoid"></div>
        <div style="position:absolute;width:100%;height:100%;opacity:.3;background:#000;"></div>
        <div class="banner_detail_box w-450px">

            <div v-if="vwe.badge && vwe.student_response.approve !== 1" class="mt-4 mb-4">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center mb-10">
                            <img :src="vwe.badge?.image_fullpath" :alt="vwe.badge.name" class="me-3" width="25" />
                            <div>
                                <p class="mb-1 fw-bold text-light fs-4">
                                    {{ vwe.badge.name }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h1 class="fw-normal text-light">Virtual Work Experience</h1>
            <h1 class="display-4 fw-normal mb-4 text-light" v-html="vwe.title"></h1>
            <div class="row text-light align-items-center">

                <div class="col-md-4 col-lg-3" v-if="vwe.estimated_time && (vwe.estimated_time.hours || vwe.estimated_time.minutes)">
                    <i class="fa-regular fa-clock text-white me-2"></i>
                    <span v-if="vwe.estimated_time && vwe.estimated_time.hours" v-text="vwe.estimated_time.hours + 'h '"></span>
                    <span v-if="vwe.estimated_time && vwe.estimated_time.minutes" v-text="vwe.estimated_time.minutes + 'm'"></span>
                </div>

                <div class="col-md-4 col-lg-3" v-if="vwe.level">
                    <i class="fa fa-chart-simple text-white me-2"></i>
                    <span v-text="vwe.level"></span>
                </div>
                <!-- <div class="col">
                    <span class="fs-6 text-light">{{ vwe.student_completed_percentage }}%</span>
                </div> -->

                <div class="col-md-5 col-lg-5 mt-lg-0 mt-md-3">

                    <!-- <span v-if=" vwe.student_completed_percentage === 100 && vwe?.student_response?.approve !== 1">

                        <span class="text-dark px-5 py-2 rounded-pill w-auto d-inline-block"
                            style="background-color: #CDD6DD;">
                            Submitted For Review
                        </span>
                    </span>

                    <span v-if="vwe.student_completed_percentage === 100 && vwe?.student_response?.approve === 1" class="fs-6 text-light px-5 py-2 rounded-pill w-100" style="background-color: #0062ff">
                        Completed
                    </span>
                    <span v-else-if="
                        vwe.student_completed_percentage > 0 &&
                        vwe.student_completed_percentage < 100
                    " class="fs-6 text-dark px-5 py-2 rounded-pill" style="background-color: #e9ff1f">
                        {{ vwe.student_completed_percentage }}% Completed
                    </span> -->
                    <ScormResultStatusBadge
                        :status="scormResult?.lesson_status"
                        :completion-percentage="vwe.student_completed_percentage"
                    />
                </div>


            </div>
            <AnzscoDetails :tags-grouped="anzscoTagsGrouped" />
            <div class="row mt-5">
                <div class="col-sm-6 fs-6 text-light p-2" v-for="tag in vwe.tagged" :key="tag.id">
                    <i class="fa fa-check text-white"></i> {{ tag.tag_name }}
                </div>
            </div>
            <div class="row mt-5" v-if="vwe.foreground_video">
                <div class="col-8 col-sm-6 col-md-12">
                    <button type="button" class="btn btn-black-custom btn-lg rounded-0 w-100 p-md-5" data-bs-toggle="modal" data-bs-target="#kt_modal_trailer"> Watch Trailer </button>
                </div>
            </div>
            <div class="row mt-5" v-if="vwe.student_response && vwe.student_response.status == 'Submitted'">
                <div class="col-8 col-sm-6 col-md-10">
                    <button class="btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5" style="font-size: 14px !important" v-if="vwe.student_response.response_path === ''" @click="viewResponse"> View Response </button>

                    <button v-else class="btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5" style="font-size: 14px !important" data-bs-toggle="modal" data-bs-target="#kt_modal_viewResponse">
                        View Response
                    </button>
                </div>
                <!-- <div class="col-sm-6 col-md-2 text-center my-auto">
                    <div v-if="vwe.student_completed_percentage >= 100">
                        <p class="cursor-pointer fs-5 text-light d-flex gap-1 my-auto" data-bs-toggle="modal" data-bs-target="#kt_modal_reset_responses">
                            <i class="fa-solid fa-rotate-right fs-5 text-light my-auto "></i> Reset
                        </p>
                    </div>
                </div> -->
            </div>


            <div class="row row-cols-3 mt-5">

                <div v-if="vwe.badge && vwe.student_response.badge_key && vwe?.student_response?.approve === 1 && vwe.student_completed_percentage === 100 && scormModulePassed" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" @click="openBadgeModal(vwe.student_response.badge_key)">
                                <img :src="vwe.badge?.image_fullpath" :alt="vwe.badge.name" class="me-3" width="25" />
                                <div class="overflow-hidden">
                                    <p class="fw-bold text-light my-auto">
                                        View Badge
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="vwe?.student_response?.approve === 1 && scormModulePassed" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" @click="openCertificateModal">
                                <i class="fa-solid fa-file text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Certificate
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="
                    vwe.student_response &&
                    vwe.student_response.feedback &&
                    vwe.student_response.approve === 1
                " class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" @click="openFeedbackModal">
                                <i class="fa-solid fa-comments text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Feedback
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="vwe.student_completed_percentage === 100 && scormResult?.lesson_status" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer w-fit-content" @click="openScormModal">
                                <i class="fa-solid fa-clipboard text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Results
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
    <div :class="{ row: vwe.student_response.step_responses.length < 6, 'sticky-top': scrolled }" v-on="handleScroll" class="d-flex bg-black module-sections">
        <template v-for="step in vwe.student_response.step_responses" :key="step.step.id">
            <div class="text-center p-0" v-bind:class="[(vwe.student_response.step_responses.length < 6) ? 'col' : 'col-6 col-sm-4 col-md-2', 'bg-black']">
                <div class="module-section d-flex flex-column justify-content-center align-items-center py-5">
                    <span class="svg-icon svg-icon-primary svg-icon-2x"> <svg v-if="step" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" /> </g> </svg> <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" /> </g> </svg> </span>
                    <p class="m-0 px-5 text-white" v-html="step.step.title"></p>
                    <p class="m-0 text-white">
                        <span v-if="step.step.estimated_time && step.step.estimated_time.hours" v-text="step.step.estimated_time.hours + 'h '"></span>
                        <span v-if="step.step.estimated_time && step.step.estimated_time.minutes" v-text="step.step.estimated_time.minutes + 'm'"></span>
                        &nbsp;
                    </p>
                </div>
            </div>
        </template>
        <div class="text-center p-0" v-bind:class="[(vwe.student_response.step_responses.length < 6) ? 'col' : 'col-6 col-sm-4 col-md-2 ', 'bg-black']">
            <div class="module-section d-flex flex-column justify-content-center align-items-center py-5">
                <span class="svg-icon svg-icon-primary svg-icon-2x"> <svg v-if="vwe.student_response" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" /> </g> </svg> <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" /> </g> </svg> </span>
                <p class="m-0" :class="{ 'text-white': vwe.student_response }">
                    Final Step
                </p>
                <p class="m-0" :class="{ 'text-white': vwe.student_response }">
                    &nbsp;
                </p>
            </div>
        </div>
    </div>
    <div id="vweSections" class="section-content">
        <!-- <template v-for="step in vwe.student_response.step_responses" :key="step.step.id">
            <div class="row mb-12">
                <div class="col-12 col-md-8 offset-md-2">
                    <h3 class="fw-normal text-dark">Section {{ step.step.number }}</h3>
                    <h1 class="mb-4 text-dark" v-html="step.step.title"></h1>
                    <div v-if="step.step.estimated_time && step.step.estimated_time.hours || step.step.estimated_time && step.step.estimated_time.minutes">
                        <i class="fa-regular fa-clock text-dark me-2"></i>
                        <span v-if="step.step.estimated_time && step.step.estimated_time.hours" v-text="step.step.estimated_time.hours + 'h '"></span>
                        <span v-if="step.step.estimated_time && step.step.estimated_time.minutes" v-text="step.step.estimated_time.minutes + 'm'"></span>
                    </div>
                    <div class="my-5" v-html="step.step.body"></div>
                    <div class="froala-response" v-if="step.step.response && step.response" v-html="step.response"></div>
                </div>
            </div>
        </template> -->



        <div class="d-flex justify-content-center">
            <div class="container">
                <div class="fs-4 fw-bold mb-8 ms-md-5">{{ vwe?.student_response?.student?.name }}</div>

                <div class="row border rounded p-10 d-flex" style="min-height: 70vh;">
                    <div class=" col-lg-3 col-md-8 overflow-auto" style="max-height: 70vh;">
                        <ul class="nav nav-tabs nav-pills flex-row border-0 flex-md-column me-5 mb-3 mb-md-0 fs-6">
                            <template v-for="step in vwe.student_response.step_responses" :key="step.step.id">
                                <li class="nav-item w-100 me-0 mb-md-2">
                                    <a class="nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark" :class="{ 'active': selectedStepId === step.step.id }" @click="selectedStepId = step.step.id">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Section {{ step.step.number }}</span>
                                            <!-- <span class="fs-7" v-html="step.step.title"></span> -->
                                            <span class="fs-7 text-left text-capitalize">{{ step.step.title.toLowerCase() }}</span>

                                        </span>
                                    </a>
                                </li>
                                <!-- {{ step }} -->
                            </template>
                            <li class="nav-item w-100 me-0 mb-md-2">
                                <a class="nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark" :class="{ 'active': selectedStepId === null }" @click="selectedStepId = null">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Final Step</span>
                                            <span class="fs-7">Submission</span>
                                        </span>
                                    </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col overflow-auto" style="max-height: 70vh;">

                        <template v-for="step in vwe.student_response.step_responses" :key="step.step.id">

                            <div v-if="selectedStepId === step.step.id">

                                <router-link :to="{
                                    name: 'task-vwe-section-detail',
                                    params: { id: currentVwe, sectionid: step.step.number },
                                }"
                                custom
                                v-slot="{ href }"
                                >
                                <a
                                    :href="href"
                                    target="_blank"
                                    rel="noopener"
                                    class="d-flex justify-content-end me-3 position-sticky top-0 bg-white p-2 gap-1"
                                >
                                    <i class="fa fa-eye my-auto"></i> View Module
                                </a>
                                </router-link>

                                <div v-if="
                                    (step.step.estimated_time &&
                                        step.step.estimated_time.hours) ||
                                    (step.step.estimated_time &&
                                        step.step.estimated_time.minutes)
                                ">
                                    <i class="fa-regular fa-clock text-dark me-2"></i>
                                    <span v-if="
                                        step.step.estimated_time &&
                                        step.step.estimated_time.hours
                                    " v-text="step.step.estimated_time.hours + 'h '"></span>
                                    <span v-if="
                                        step.step.estimated_time &&
                                        step.step.estimated_time.minutes
                                    " v-text="step.step.estimated_time.minutes + 'm'"></span>
                                </div>

                                <!-- <div class="my-5" v-html="step.step.body"></div> -->

                                <div class="froala-response mb-5" v-if="step.step.response && step.response" v-html="step.response"></div>
                                <div v-if="!step.step.response && !step.step.is_scorm" class="my-2">
                                    <h3>
                                        <i class="far fa-times-circle"></i> No answer required on this section.
                                    </h3>
                                </div>
                                <SectionScormResponse
                                    v-if="step.step.is_scorm"
                                    :sections-map="sectionsMap"
                                    :current-section-id="step.step.id"
                                />
                            </div>
                            <div v-if="!selectedStepId === step.step.id">
                                    <span class="text-dark">
                                        <i class="fa-regular fa-circle-xmark text-dark"></i>
                                        <span class=""> No answer required on this section. </span>
                                    </span>
                            </div>
                        </template>

                        <div v-if="selectedStepId === null" class="text-center mt-5">
                            <h4>Students were asked to upload a document on their final step.</h4>

                            <div class="d-flex justify-content-center gap-10 pt-10">

                                <div v-if="vwe.student_response && vwe.student_response.status == 'Submitted'">

                                    <button class="btn btn-secondary rounded" style="font-size: 14px !important" @click="openResponseModal">
                                        <i class="fa fa-eye"></i>  View Response
                                    </button>
                                </div>

                                <div v-if="vwe.response && vwe.student_response.response_path" class=" d-flex gap-10">
                                    <a :href="'/workexperiencetemplates/responses/' + vwe.student_response.id +  '/download'" class="btn btn-secondary rounded"><i class="fa fa-download"></i> Download Response </a>

                                    <a v-if="vwe.student_response.approve == '1'" :href="'/certificate-download/' + vwe.student_response.id" target="_blank" class="btn btn-secondary rounded"><i class="fa fa-download"></i> Download Certificate </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- <div class="row" v-if="vwe.response && vwe.student_response.response_path">
            <div class="col-12 col-md-8 offset-md-2">
                <h1 class="fw-normal mb-4 text-dark">
                    Uploaded file:
                </h1>
                <div class="my-5" v-text="vwe.student_response.filename"></div>
                <div class="my-5">
                    <a :href="'/workexperiencetemplates/responses/' + vwe.student_response.id + '/download'" class="btn btn-primary rounded-0">Download Response</a>
                    <a v-if="vwe.student_response.approve == '1'" :href="'/certificate-download/' + vwe.student_response.id" target="_blank" class="btn btn-primary rounded-0 ms-2">Download Certificate</a>
                </div>
            </div>
        </div>

        <div class="row mt-3" v-if="vwe.feedback">
            <div class="bg-light teacher-feedback col-12 col-md-8 offset-md-2">
                <h1 class="fw-normal mb-4 text-dark">
                    Feedback
                </h1>
                <div class="my-5" v-html="vwe.feedback"></div>
            </div>
        </div> -->

    </div>
    <div class="modal fade" id="kt_modal_trailer" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-900px">
            <div class="modal-content rounded-0">
                <div class="modal-body bg-black p-1" v-html="vwe.foreground_video"></div>
            </div>
        </div>
    </div>
    <!-- <div class="modal fade" id="kt_modal_reset_responses" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <p>Are you sure you want to remove all of your responses?</p>
                    <button type="button" class="btn btn-primary btn-sm rounded-0" data-bs-dismiss="modal" @click="resetVwe(vwe.id)">Yes</button>
                    <button type="button" class="btn btn-sm btn-primary rounded-0 m-5" data-bs-dismiss="modal">No</button>
                </div>
            </div>
        </div>
    </div> -->

    <div class="modal fade" id="kt_modal_viewFile" tabindex="-1" aria-hidden="true">
        <div :class="['modal-dialog modal-dialog-centered', isFullscreen ? 'custom-fullscreen-modal' : 'mw-1200px']">
            <div class="modal-content rounded-0 mt-5">
                <div class="modal-header py-3">
                    <h5 class="modal-title">Certificate Preview</h5>
                    <div>
                        <span class="mx-4 cursor-pointer" @click="toggleFullscreen">
                            <i v-if="isFullscreen" class="fa-solid fa-compress text-black"></i>
                            <i v-else class="fa-solid fa-expand text-black"></i>
                        </span>
                        <a v-if="vwe.student_response.approve == '1'" :href="'/certificate-download/' + vwe.student_response.id" target="_blank" class="text-secondary mx-2"><i class="fa-solid fa-download text-black"></i></a>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body bg-black p-1 text-white text-center">
                    <iframe v-if="certificateUrl" :src="certificateUrl" class="w-100" :style="{ height: isFullscreen ? '90vh' : '80vh', border: 'none' }" allowfullscreen></iframe>

                    <p v-else>Loading...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="kt_modal_feedback" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-600px">
            <div class="modal-content rounded-0" style="height: 80vh">
                <div class="modal-header text-white">
                    <h5 class="modal-title">Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 bg-gray-50 text-left">
                    <div class="p-4 bg-white" style="height: 90%">
                        <p v-html="vwe.student_response.feedback" class="text-gray-700"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <ScormResultModal
        ref="scormModalRef"
        v-if="scormResult?.lesson_status"
        :module="vwe"
        trackable-type="steps"
        :redo-success-route="{
            name: `task-vwe-section-detail`,
            params: {
                id: vwe.scorm_scoring_step_result?.template_id,
                sectionid: vwe.scorm_scoring_step_result?.number,
            },
        }"
        @viewBagdeDetails="openBadgeModal(vwe.user_response.badge_key)"
    />

    <BadgeModal :selectedBadge="selectedBadge" :module-data="vwe"  @shareBadge="openShareBadgeModal" />
    <ResponseModal :modalSrc="modalSrc" :downloadUrl="downloadUrl"/>


</template>

<script lang="ts">
    import {
        defineComponent,
        ref,
        onMounted,
        computed
    } from "vue";
    import ApiService from "@/core/services/ApiService";
    import {
        useStore
    } from "vuex";
    import {
        useRoute
    } from "vue-router";
    import Swal from "sweetalert2/dist/sweetalert2.min.js";
    import iframeResize from 'iframe-resizer/js/iframeResizer';
    import BadgeModal from "../Badges/BadgeModal.vue";
    import ResponseModal from "@/components/tasks/ResponseModal.vue";
    // SCORM and ANZSCO - START
    import AnzscoDetails from "@/components/modules/detail/AnzscoDetails.vue";
    import ScormResultStatusBadge from "@/components/modules/response/ScormResultStatusBadge.vue";
    import ScormResultModal from "@/components/modules/response/ScormResultModal.vue";
    import SectionScormResponse from "@/components/modules/response/SectionScormResponse.vue";
    // SCORM and ANZSCO - END

    // Import Bootstrap
    import * as bootstrap from 'bootstrap';

    interface Badge {
        id: number;
        name: string;
        description?: string;
        image_fullpath: string;
        formatted_issue_date: string;
        formatted_expiration_date: string;
    }


    export default defineComponent({
        name: "vwe-detail",
        components: {
        BadgeModal,
        ResponseModal,
        // SCORM and ANZSCO - START
        ScormResultModal,
        AnzscoDetails,
        ScormResultStatusBadge,
        SectionScormResponse,
        // SCORM and ANZSCO - END
    },
        setup() {
            const store = useStore();
            const route = useRoute();
            const modalSrc = ref<string>('');
            const modalId = ref('')
            const downloadUrl = ref<string>('');


            const currentUser = store.getters.currentUser;
            onMounted(async () => {
                await fetchVweDetail();
                iframeResize({
                    heightCalculationMethod: 'bodyScroll'
                }, '.section-content iframe')
            });

            onMounted(() => {
            const modalEl = document.getElementById('kt_modal_viewResponse')

            if (!modalEl) {
                console.warn('Modal element not found: #kt_modal_viewResponse')
                return
            }

            modalEl.addEventListener('show.bs.modal', (event: any) => {
                const button = event.relatedTarget as HTMLElement
                if (button) {
                    modalSrc.value = vwe.value.student_response.view_response_file_path;

                    modalId.value = vwe.value.student_response.id;

                    downloadUrl.value = `workexperiencetemplates/responses/${modalId.value}/download`;
                }
            })
        })


            const vwe = ref();
            const currentVwe = ref();
            const studentId = route.params.student;
            const setVwe = ref();
            const scrolled = ref(false);
            const selectedStepId = ref(null);
            const selectedBadge = ref<Badge | object>({});
            const certificateUrl = ref<string>('');
            const isFullscreen = ref();


            let sectionNavOffsetTop = 0;
            vwe.value = {
                id: 1,
                background_imagepath: null,
                background_video: null,
                student_response: {
                    step_responses: {}
                },
                scorm_scoring_step_result: {
                    user_scorm_result: {}
                },
                student_completed_percentage: null,
            };
            currentVwe.value = route.params.id;

            const viewResponse = () => {
                // document.querySelector('.module-section') !.scrollIntoView({ behavior: 'smooth' });
                var banner = document.querySelector('.banner');
                window.scrollBy({
                    top: banner!.scrollHeight, // could be negative value
                    left: 0,
                    behavior: 'smooth'
                });
            };

            const fetchVweDetail = async () => {
                try {
                    const { data } = await ApiService.get('api/vwe/' + currentVwe.value + '/' + studentId);

                    vwe.value = data;
                    vwe.value.user_response.step_responses.sort(
                        (a, b) => a.step.number - b.step.number
                    );

                    var banner = document.getElementById('banner');
                    sectionNavOffsetTop = banner!.scrollHeight + 120;

                    if (data.user_response?.response_path === "") {
                        waitForSectionAndScroll();
                    }
                } catch (error) {
                    console.log(error);
                };
            };

            const handleScroll = () => {
                var windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
                if (windowWidth > 991) {
                    var element = document.getElementById('kt_app_toolbar') as HTMLElement;
                    if (window.scrollY > sectionNavOffsetTop) {
                        scrolled.value = true;
                        element.style.display = "none";
                    } else {
                        scrolled.value = false;
                        element.style.display = "flex";
                    }
                }
            }
            // const resetVwe = (id) => {
            //     setVwe.value = {
            //         id: id
            //     };

            //     console.log(setVwe.value);
            //     ApiService.post(`api/vwe/` + id + `/reset`, setVwe.value)
            //         .then(({
            //             data
            //         }) => {
            //             Swal.fire({
            //                 text: "This virtual work experience and your previous responses have been reset.",
            //                 icon: "success",
            //                 buttonsStyling: false,
            //                 confirmButtonText: "Ok",
            //                 customClass: {
            //                     confirmButton: "btn fw-semobold btn-light-primary rounded-0",
            //                 },
            //             }).then(() => {
            //                 // window.location.reload();
            //                 window.location.replace('#/tasks/vwe/' + id);
            //             });
            //             // vwe.value.favourite = data.favourite;
            //         })
            //         .catch(({
            //             response
            //         }) => { });
            // };

            const openBadgeModal = (badge: Badge) => {
                selectedBadge.value = badge;
                // Use Bootstrap's Modal API to show the modal
                const modalElement = document.getElementById('kt_modal_badge');
                if (modalElement) {
                    // Check if there's an existing modal instance
                    let bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (!bsModal) {
                        // If no instance exists, create a new one
                        bsModal = new bootstrap.Modal(modalElement, {
                            backdrop: 'static' // Prevents closing when clicking outside
                        });
                    }
                    bsModal.show();
                }
            };

            const openShareBadgeModal = (badge: Badge) => {
                selectedBadge.value = badge;
                // Use Bootstrap's Modal API to show the modal
                const modalElement = document.getElementById('kt_modal_share_badge');
                if (modalElement) {
                    // Check if there's an existing modal instance
                    let bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (!bsModal) {
                        // If no instance exists, create a new one
                        bsModal = new bootstrap.Modal(modalElement, {
                            backdrop: 'static' // Prevents closing when clicking outside
                        });
                    }
                    bsModal.show();
                }
            };

            const openFeedbackModal = () => {
                // Use Bootstrap's Modal API to show the modal
                const modalElement = document.getElementById('kt_modal_feedback');
                if (modalElement) {
                    // Check if there's an existing modal instance
                    let bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (!bsModal) {
                        // If no instance exists, create a new one
                        bsModal = new bootstrap.Modal(modalElement, {
                            backdrop: 'static' // Prevents closing when clicking outside
                        });
                    }
                    bsModal.show();
                }
            };

            const openCertificateModal = () => {
                loadCertificate();
                // Use Bootstrap's Modal API to show the modal
                const modalElement = document.getElementById('kt_modal_viewFile');
                if (modalElement) {
                    // Check if there's an existing modal instance
                    let bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (!bsModal) {
                        // If no instance exists, create a new one
                        bsModal = new bootstrap.Modal(modalElement, {
                            backdrop: 'static' // Prevents closing when clicking outside
                        });
                    }
                    bsModal.show();
                }
            };

            const openResponseModal = () => {
                // Set up the modal data
                if (vwe.value && vwe.value.student_response) {
                    modalSrc.value = vwe.value.student_response.view_response_file_path;
                    modalId.value = vwe.value.student_response.id;
                    downloadUrl.value = `workexperiencetemplates/responses/${modalId.value}/download`;
                }

                // Use Bootstrap's Modal API to show the modal
                const modalElement = document.getElementById('kt_modal_viewResponse');
                if (modalElement) {
                    // Check if there's an existing modal instance
                    let bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (!bsModal) {
                        // If no instance exists, create a new one
                        bsModal = new bootstrap.Modal(modalElement, {
                            backdrop: 'static' // Prevents closing when clicking outside
                        });
                    }
                    bsModal.show();
                }
            };

            const loadCertificate = async () => {
                if (!vwe.value || !vwe.value.student_response) {
                    console.error("vwe.student_response is missing!");
                    return;
                }
                const fileId = vwe.value.student_response.id;
                certificateUrl.value = `/certificate-download/${fileId}?preview=true`;
            };

            const toggleFullscreen = () => {
                isFullscreen.value = !isFullscreen.value;
            };

            const waitForSectionAndScroll = (retries = 10) => {
            const section = document.getElementById("vweSections");
                if (section) {
                    section.scrollIntoView({ behavior: "smooth", block: "start" });
                    console.log("Scrolled to #vweSections");
                } else if (retries > 0) {
                    setTimeout(() => waitForSectionAndScroll(retries - 1), 300); // Retry every 300ms
                } else {
                    console.log("#vweSections not found after retries");
                }
            };

            // SCORM -START
            const anzscoTagsGrouped = computed(() => {
                return vwe.value.anzsco_tag_names_grouped;
            });
            const scormModalRef = ref();
            const openScormModal = () => {
                scormModalRef.value?.openModal();
            }
            const scormResult = computed(() => {
                return vwe.value?.scorm_scoring_step_result?.user_scorm_result;
            });
            const sectionsMap = computed(() => {
                return new Map<number, any>(vwe.value.steps.map(step => [step.id, step]))
            });
            const scormModulePassed = computed(() => {
                if (scormResult.value?.lesson_status) { // If scorm scoring step exists with its result
                    return !['failed','incomplete'].includes(scormResult.value.lesson_status);
                }

                return true;
            });
            // SCORM - END

            return {
                currentUser,
                vwe,
                currentVwe,
                config: {
                    key: "hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",
                    height: 300,
                    attribution: false,
                    toolbarButtons: [''],
                    events: {
                        initialized: function () {
                            console.log('initialized')
                        }
                    }
                },
                scrolled,
                handleScroll,
                // resetVwe,
                viewResponse,
                selectedStepId,
                openBadgeModal,
                openShareBadgeModal,
                openFeedbackModal,
                openCertificateModal,
                openResponseModal,
                selectedBadge,
                downloadUrl,
                modalSrc,
                loadCertificate,
                certificateUrl,
                isFullscreen,
                toggleFullscreen,
                waitForSectionAndScroll,
                // SCORM and ANZSCO - START
                anzscoTagsGrouped,
                scormModalRef,
                openScormModal,
                scormResult,
                sectionsMap,
                scormModulePassed,
                // SCORM and ANZSCO - END
            };
        },
        props: ["id"],
        created() {
            window.addEventListener("scroll", this.handleScroll);
        },
        destroyed() {
            window.removeEventListener("scroll", this.handleScroll);
        }
    });
</script>

<style>
    .app-container {
        background-color: #fff;
    }

    .wrap {
        overflow: hidden;
        max-width: 75ch;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .btn-white-custom {
        background: #fff;
        color: #000;
    }

    .btn-black-custom:hover,
    .btn-white-custom {
        background-color: #fff !important;
        color: #000 !important;
    }

    .btn-black-custom,
    .btn-white-custom:hover {
        background-color: #000 !important;
        color: #fff !important;
    }

    .btn-white-custom:hover,
    .btn.btn-white-custom:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }

    .module-sections {
        overflow: auto hidden;
        margin-left: -30px;
        margin-right: -30px;
        position: relative;
    }

    .sticky-top {
        position: fixed;
        min-width: calc(100% - 140px);
    }

    .module-section {
        border-top: 1px solid;
        border-bottom: 1px solid;
        border-left: 1px solid;
        cursor: pointer;
        height: 100px;
    }

    .module-sections>.text-center:last-of-type>.module-section {
        border-right: 1px solid;
    }

    .app-content {
        padding: 0px;
    }

    .banner_detail_box {
        position: absolute;
        top: 50%;
        left: 20%;
        transform: translate(-50%, -50%);
    }

    .modal-backdrop {
        opacity: 0.8 !important;
    }


    /* .sticky-top+.section-content {
                margin-top: 150px;
            } */

    .section-content {
        margin-top: 50px;
        padding-bottom: 50px;
    }

    .section-content iframe {
        width: 100% !important;
    }

    .section-content iframe.wistia_embed {
        height: 100% !important;
    }

    .section-content img {
        max-width: 100%;
    }

    .section-content p img,
    .section-content p iframe {
        margin-bottom: -1rem;
    }

    .pointer {
        cursor: pointer;
    }

    .overlay {
        overflow: overlay;
    }

    .related {
        right: 5% !important;
    }

    .banner {
        background-color: #000;
        background-image: url("/images/vwe/home-parallax.jpg");
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
        min-height: calc(56.25vw - 149px);
    }

    .full-view-banner {
        margin-left: -30px;
        margin-right: -30px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        /* height: 100%; */
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .froala-response,
    .teacher-feedback {
        height: 300px;
        overflow: auto;
        padding: 20px;
        border-radius: 10px;
    }

    .froala-response {
        background-color: #fff;
        border: 1px solid #bbb;
    }

    .froala-response iframe {
        width: 100%;
    }

    .froala-response img {
        max-width: 100%;
    }

    div#kt_app_content {
        padding-top: 0px;
        padding-bottom: 0px;
    }

    @media (max-width: 1280px) {
        .banner {
            /* height: calc(56.25vw - 140px); */
            height: 56.25vw;
        }

        .banner_detail_box {
            left: 40%;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }

    @media (min-width: 992px) {

        .sticky-top+.section-content {
            margin-top: 100px;
        }

        .module-sections {
            animation-name: backtooriginal;
            animation-duration: 0.2s;
            animation-fill-mode: forwards;
            z-index: 100;
        }

        .sticky-top {
            animation-name: stick-top;
            animation-duration: 0.2s;
            animation-fill-mode: forwards;
        }

        @keyframes stick-top {
            from {
                top: 5px;
            }

            100% {
                top: 0px;
            }
        }

        @keyframes backtooriginal {
            from {
                top: -5px;
            }

            100% {
                top: 0px;
            }
        }
    }

    @media (max-width: 991px) {

        .full-view-banner,
        .module-sections {
            margin-left: -20px;
            margin-right: -20px;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

        .sticky-top {
            top: 119px;
            min-width: 100%;
        }

        .module-section {
            height: 100px;
        }
    }



    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 86.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {
        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {

        .banner {
            height: calc(100vh - 300px);
        }

        .banner_detail_box {
            left: 50%;
        }

        .sticky-top {
            margin-top: 10px;
        }
    }

    @media (max-width: 575px) {
        div#kt_app_content {
            padding-top: 30px;
        }

        .banner_detail_box {
            width: 70vw !important;
        }

        .full-view-banner {
            margin-top: 0;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(90vw + 90vh) !important;
        }

    }
</style>
