<?php

namespace App;

use App\Services\TimelineService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use App\Traits\GeneratesBadgeKey;
use Illuminate\Database\Eloquent\Casts\Attribute;

class WorkexperienceResponse extends Model
{
    use GeneratesBadgeKey;

    protected $guarded = [];
    protected $appends = ['response_file_s3_path', 'view_response_file_path'];


    protected static function booted()
    {

        static::deleting(function ($response) {
            Storage::cloud()->delete($response->response_path);
            if ($response->userExperience) {
                $response->userExperience->delete();
            }

            if ($response->activityLog) {
                $response->activityLog()->delete();
            }

            if ($response->badgeKey) {
                $response->badgeKey->delete();
            }
        });

        static::saving(function ($response) {
            if($response->status == 'Submitted'){
                $category  = ExperienceCategory::firstOrCreate(['category' => 'Virtual Work Experience'])->id;
                $startDate = $endDate = Carbon::parse($response->created_at)->format('d/m/Y');
                if ($response->time) {
                    $duration = CarbonInterval::createFromFormat('H:i:s', $response->time)->totalSeconds;
                    $durationForHumans  = CarbonInterval::seconds($duration)->cascade()->forHumans();
                    $startDate = Carbon::parse($response->created_at)->sub($durationForHumans)->format('d/m/Y');
                }

                UserExperience::updateOrCreate(
                    ['workexperience_response_id' => $response->id],
                    [
                        'user_id' => $response->student_id,
                        'experience_category_id' => $category,
                        'title' => $response->template->title,
                        'employer' => 'The Careers Department',
                        'location' => 'Online',
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'duration' => $duration
                    ]
                );
            }
        });

        static::saved(function ($response) {
            $response->load('template');
            if ($response->approve && $response->template->badge_id) {

                $scormStatus = $response->template->userScormModuleResult?->lesson_status;

                if ($scormStatus && in_array($scormStatus, ['failed', 'incomplete'])) {
                    // delete badge assigned if exists
                    if ($response->badgeKey) {
                        $response->badgeKey->delete();
                    }
                    return;
                }
                
                if (!$response->badgeKey) {
                    // ✅ Generate a new unique badge key
                    $badgeKey = self::generateBadgeKey();
                    $response->badgeKey()->create(['badge_id' => $response->template->badge_id, 'credential_id' => $badgeKey]);
                }
            }
        });

        static::created(function ($response) {
            if($response->status == 'Submitted') {
                (new TimelineService)->log(
                    $response, // model
                    'created', // event
                );
            }
        });

        static::updated(function ($response) {
            if($response->status == 'Submitted') {
                (new TimelineService)->log(
                    $response, // model
                    'updated', // event
                );
            }

        });
    }

    public function getUserAndTemplateAttribute()
    {
        $extention = pathinfo($this->response_path, PATHINFO_EXTENSION);
        $file = mb_ereg_replace("([^\w\s\d\-_~,;\[\]\(\).])", '', $this->student->name . '-' . $this->template->title);
        $file = mb_ereg_replace("([\.]{2,})", '', $file);
        $file = str_replace(" ", '_', $file);
        $filename = $file . '.' . $extention;
        return $filename;
    }

    public function student()
    {
        return $this->belongsTo(User::class, 'student_id')->whereIn('role_id', [3, 4]);
    }

    public function template()
    {
        return $this->belongsTo(WorkexperienceTemplate::class, 'template_id');
    }

    public function getSubmittedAtAttribute($value)
    {
        return Carbon::parse($value)->format('M d, Y g:i a');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function userExperience()
    {
        return $this->hasOne(UserExperience::class);
    }

    public function stepResponses()
    {
        return $this->hasMany(VweStepResponse::class);
    }

    public function scopeSubmitted($query)
    {
        return $query->whereStatus('Submitted');
    }

    public function activityLog()
    {
        return $this->morphOne(ActivityLog::class, 'subject');
    }

    protected function responseFileS3Path(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->response_path ? Storage::url($this->response_path): null
        );
    }

    protected function viewResponseFilePath(): Attribute
    {
        return Attribute::make(
            get: fn() => match (pathinfo($this->response_path, PATHINFO_EXTENSION)) {
                'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx' => 'https://view.officeapps.live.com/op/view.aspx?src=' . urlencode($this->response_file_s3_path),
                'pdf' => $this->response_file_s3_path,
                default => 'unsupported',
            }
        );
    }

    public function badgeKey()
    {
        return $this->morphOne(BadgeKey::class, 'badgeable');
    }
}
