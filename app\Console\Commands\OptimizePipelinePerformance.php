<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Employer;
use App\Services\EmployerPipelineService;
use Illuminate\Support\Facades\DB;

class OptimizePipelinePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pipeline:optimize {--test : Run performance test} {--clear-cache : Clear all pipeline cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize employer pipeline performance and run tests';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->option('clear-cache')) {
            $this->clearAllCache();
            return 0;
        }

        if ($this->option('test')) {
            $this->runPerformanceTest();
            return 0;
        }

        $this->info('Pipeline optimization tools:');
        $this->info('--test: Run performance comparison test');
        $this->info('--clear-cache: Clear all pipeline cache');
        
        return 0;
    }

    /**
     * Clear all pipeline cache
     */
    private function clearAllCache()
    {
        $this->info('Clearing all pipeline cache...');
        
        $pipelineService = app(EmployerPipelineService::class);
        $employers = Employer::all();
        
        $this->withProgressBar($employers, function ($employer) use ($pipelineService) {
            $pipelineService->clearPipelineCache($employer);
        });
        
        $this->newLine();
        $this->info('Pipeline cache cleared for ' . $employers->count() . ' employers.');
    }

    /**
     * Run performance test comparing old vs new implementation
     */
    private function runPerformanceTest()
    {
        $this->info('Running pipeline performance test...');
        
        // Get a sample of employers to test
        $employers = Employer::with('company')->limit(5)->get();
        
        if ($employers->isEmpty()) {
            $this->error('No employers found to test.');
            return;
        }

        $pipelineService = app(EmployerPipelineService::class);
        
        $this->info('Testing with ' . $employers->count() . ' employers...');
        
        foreach ($employers as $employer) {
            if (!$employer->company) {
                continue;
            }
            
            $this->info("Testing employer: {$employer->name} (Company: {$employer->company->detail->name ?? 'N/A'})");
            
            // Test new optimized method
            $start = microtime(true);
            $newCount = $pipelineService->getCachedPipelineCount($employer);
            $newTime = microtime(true) - $start;
            
            // Test cached top locations
            $start = microtime(true);
            $topLocations = $pipelineService->getCachedTopLocations($employer, 3);
            $locationsTime = microtime(true) - $start;
            
            $this->line("  Pipeline count: {$newCount} (took " . round($newTime * 1000, 2) . "ms)");
            $this->line("  Top locations: {$topLocations->count()} states (took " . round($locationsTime * 1000, 2) . "ms)");
            
            // Show memory usage
            $memoryUsage = memory_get_usage(true) / 1024 / 1024;
            $this->line("  Memory usage: " . round($memoryUsage, 2) . "MB");
            
            $this->newLine();
        }
        
        $this->info('Performance test completed!');
        $this->info('The optimized version uses:');
        $this->info('- UNION queries instead of OR conditions');
        $this->info('- Proper database indexes');
        $this->info('- Caching for repeated operations');
        $this->info('- Reduced memory usage');
    }
}
