<?php

namespace App\Http\Controllers\Auth;

use App\ChildInvitee;
use App\ChildParent;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\ParentInvitee;

use App\Http\Controllers\Controller;
use App\IndividualStudent;
use App\Role;
use App\Standard;
use App\Stage;
use App\User;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Stripe\Stripe;
use App\Country;
use App\Events\LicenseRenewed;
use DB;
use App\Licence;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Jobs\ProcessParentInvitation;
use Illuminate\Support\Facades\URL;
use Stripe\Checkout\Session;

class IndividualLicenseController extends Controller
{

    public function storeIndividualLicense(Request $request)
    {
        // dd(config('services.stripe.individual_key'));
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $roleId = Role::where('name', "Individual Student")->value('id');
            $student = IndividualStudent::create([
                'name' => request('firstname') . " " . request('lastname'),
                'email' => (request('email')) ? request('email') : Str::random(40),
                'password' => bcrypt(Str::random(40)),
                'role_id' => $roleId,
            ]);

            if ($student) {
                $user = User::find($student->id);
                $user->newSubscription('Individual', config('services.stripe.individual_key'))->create($request->stripeToken);
            }

            $student->profile()->create([
                'firstname' => request('firstname'),
                'lastname' => request('lastname'),
            ]);

            $account = ChildInvitee::create([
                'child_id' => $student->id,
                'token' => uniqid(Str::random(27)),
                'processed' => '0',
            ]);
            return redirect('individual/createaccount/' . $account->token)->with('message', 'Payment successful! Please fill in your details below to complete your registration.');
        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }

    public function stripeCheckoutSession(Request $request)
    {
        // Set API key before any Stripe operations
        Stripe::setApiKey(config('services.stripe.secret'));
        $form = serialize($request->all());
        $email = $request->email;
        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'subscription_data' => [
                // 'trial_period_days' => 30,
            ],
            'customer_email' => $email,
            'line_items' => [
                [
                    'price' => config('services.stripe.individual_key'),
                    'quantity' => 1,
                ]
            ],
            'mode' => 'subscription',
            'allow_promotion_codes' => true,
            'success_url' => route('stripe.individualsuccess'),
            'cancel_url' => route('stripe.individualfailed'),
        ]);
        $request->session()->put('cart.individuallicense', ['form' => $form, 'session' => $session]);
        return $session;
    }

    public function stripeSuccess(Request $request)
    {
        $cart = $request->session()->get('cart.individuallicense');
        // dd($cart);
        $request->session()->forget('cart.individuallicense');
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {
                    $form = unserialize($cart['form']);
                    // dd($form);
                    /*Payment successful*/
                    $stripe_id = $sessiondetail->customer;
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();

                    $roleId = Role::where('name', "Individual Student")->value('id');
                    $student = IndividualStudent::create([
                        'name' => $form['studentDetail']['firstName'] . " " . $form['studentDetail']['lastName'],
                        'email' => ($form['email']) ? $form['email'] : Str::random(40),
                        'password' => bcrypt($form['studentDetail']['password']),
                        'role_id' => $roleId,
                        'stripe_id' => $stripe_id,
                        'state_id' => $form['studentDetail']['state'],
                        'postcode' => $form['studentDetail']['postcode']
                    ]);

                    if ($student) {

                        $user = User::find($student->id);
                        $pm = $user->paymentMethods();
                        if ($pm) {
                            $user->addPaymentMethod($pm->first()->id);
                            $card = $user->paymentMethods()->first()->card;
                            $user->card_last_four = $card->last4;
                            $user->card_brand = $card->brand;
                            $user->save();
                        }

                        $student->profile()->create([
                            'firstname' => $form['studentDetail']['firstName'],
                            'lastname' => $form['studentDetail']['lastName'],
                        ]);

                        $school = null;
                        if ($form['studentDetail']['inSchool'] == 'inschool') {
                            $standard_id = $form['studentDetail']['year'];
                            if (!empty($form['studentDetail']['school']['id'])) {
                                $school = $form['studentDetail']['school']['id'];
                            }
                            $graduate_year = null;
                        } else {
                            // $stage_id = request('stage_id');
                            $graduate_year = $form['studentDetail']['gradYear'];
                            $school = null;
                            $standard_id = Standard::nonStudentId();
                        }

                        if ($form['studentDetail']['genderOther']) {
                            $gender = $form['studentDetail']['genderOther'];
                        } else {
                            $gender = $form['studentDetail']['gender'];
                        }

                        $student->profile()->update([
                            'gender' => $gender,
                            'standard_id' => $standard_id,
                            // 'stage_id' => $stage_id,
                            'graduate_year' => $graduate_year,
                            'school' => $school,
                            'accountcreated' => true,
                        ]);

                        \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));
                        if ($student->is_child) {
                            // User::updateAssociatedUsersMailchimpDetail($student->id);
                        }
                        $user = User::find($student->id);
                        // UpdateMailchimpAudienceJob::dispatch($user);


                        // $account = ChildInvitee::create([
                        //     'child_id' => $student->id,
                        //     'token' => uniqid(Str::random(27)),
                        //     'processed' => '0',
                        // ]);
                        $licence = new Licence();
                        $licence->stripe_id = $subscriptionid;
                        $licence->purchased_by = $student->id;
                        $licence->assigned_to = $student->id;
                        $licence->number = strtoupper(sha1(time()));
                        $licence->type = 'Individual';
                        $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                        $licence->save();
                    }

                    DB::commit();
                    if (Auth::attempt(['email' => $student->email, 'password' => $form['studentDetail']['password']])) {
                        // return redirect('/#/gameplan')->with('message', "Your account has been successfully created.");
                        $request->session()->regenerate();
                        if (!empty($form['studentDetail']['parent']['email'])) {
                            $invitation = ParentInvitee::create([
                                'child_id' => Auth::id(),
                                'relation' => "parent",
                                'email' => $form['studentDetail']['parent']['email'],
                                'token' => uniqid(Str::random(27)),
                                'processed' => '0',
                            ]);

                            dispatch(new ProcessParentInvitation($invitation))->afterCommit();
                        }
                        return redirect(route('landing') . '#/dashboard');
                    }
                    // return redirect('individual/createaccount/' . $account->token)->with('message', 'Payment successful! Please fill in your details below to complete your registration.');
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect(route('landing') . '#/sign-in')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function stripeCheckoutLogin(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));
        $form = serialize($request->all());
        $email = $request->email;

         $session = Session::create([
            'payment_method_types' => ['card'],
            'subscription_data' => [
                // 'trial_period_days' => 30,
            ],
            'customer_email' => $email,
            'line_items' => [
                [
                    'price' => config('services.stripe.individual_key'),
                    'quantity' => 1,
                ]
            ],
            'mode' => 'subscription',
            'allow_promotion_codes' => true,
            'success_url' => route('stripe.individualsuccesslogin'),
            'cancel_url' => route('stripe.individualfailed'),
        ]);
        $request->session()->put('cart.individuallicense', ['form' => $form, 'session' => $session]);
        return $session;
    }


    public function stripeSuccessLogin(Request $request)
    {
        $cart = $request->session()->get('cart.individuallicense');
        $request->session()->forget('cart.individuallicense');
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {
                    $stripe_id = $sessiondetail->customer;
                    $form = unserialize($cart['form']);
                    /*Payment successful*/
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();

                    // $roleId = Role::where('name', "Individual Student")->value('id');
                    // $student = IndividualStudent::create([
                    //     'name' => $form['firstname'] . " " . $form['lastname'],
                    //     'email' => ($form['email']) ? $form['email'] : Str::random(40),
                    //     'password' => bcrypt(Str::random(40)),
                    //     'role_id' => $roleId,
                    // ]);


                    $user = User::whereEmail($form['email'])->first();
                    if ($user) {
                        $user->stripe_id = $stripe_id;
                        $user->save();

                        $pm = $user->paymentMethods();
                        if ($pm) {
                            $user->addPaymentMethod($pm->first()->id);
                            $card = $user->paymentMethods()->first()->card;
                            $user->card_last_four = $card->last4;
                            $user->card_brand = $card->brand;
                            $user->save();
                        }

                        $licence = Licence::firstOrCreate(
                            ['assigned_to' => $user->id, 'type' => 'Individual'],
                            [
                                'stripe_id' => $subscriptionid,
                                'purchased_by' => $user->id,
                                'number' => strtoupper(sha1(time())),
                                'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                            ]
                        );
                        \Event::dispatch(new LicenseRenewed(User::where("id", $user->id)->first()));

                        DB::commit();
                        $latestGameplan = $user->latestGameplan();
                        $redirectTo = $latestGameplan ? '/#/dashboard' : '/#/gameplan';
                        Auth::login($user);

                        return redirect($redirectTo);
                        // return redirect('/login')->with('message', 'Payment successful! Please login normally.');
                    }
                    DB::rollback();
                    return redirect('/login')->with('message', "Whoops! Something went wrong. Please try again.");
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect('/login')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function stripeFailed()
    {
        return redirect('individual/buylicense')->with('message', "Whoops! Something went wrong. Please try again.");
    }

    public function searchlastname()
    {
        $roleId = Role::where('name', "Individual Student")->value('id');
        $result = [];
        $term = request('q');
        $students = ['results' => [], 'pagination' => ['more' => false]];
        if ($term) {
            $students = IndividualStudent::select('id', 'name', 'email')->whereHas('profile', function ($query) use ($term) {
                $query->where('lastname', $term)->where('accountcreated', '0');
            })->where('role_id', $roleId)->paginate(10);
            return $students;
        }
    }

    public function createaccount($token)
    {
        $years = Standard::onlySchool()->get();
        $stages = Stage::All();
        $countries = Country::All();
        $id = ChildInvitee::where('token', $token)->value('child_id');
        if ($id) {
            $student = User::where('id', $id)->with('profile')->first();
            if ($student->profile->accountcreated) {
                return redirect('/login')->with('message', 'Looks like you already have gone through this step. Please login with your email and password');
            }
            return view('auth.license.register', compact('student', 'years', 'countries', 'stages'));
        }
        abort(404);
    }

    public function register(Request $request)
    {
        $student = User::findOrFail(request('id'));
        if ($student->profile->accountcreated) {
            return redirect('/login')->with('message', 'Looks like you already have gone through this step . Please login with your email and password');
        }
        $student->name = request('firstname') . " " . request('lastname');
        $student->password = bcrypt(request('password'));
        $student->state_id = request('state');
        $student->postcode = request('postcode');
        $student->save();

        if (request('stage') == 'school') {
            $standard_id = request('year');
            $school = request('school');
            $graduate_year = null;
        } else {
            // $stage_id = request('stage_id');
            $graduate_year = request('graduate_year');
            $school = null;
            $standard_id = Standard::nonStudentId();
        }

        if (request('other_gender')) {
            $gender = request('other_gender');
        } else {
            $gender = request('gender');
        }

        $student->profile()->update([
            'firstname' => request('firstname'),
            'lastname' => request('lastname'),
            'gender' => $gender,
            'standard_id' => $standard_id,
            // 'stage_id' => $stage_id,
            'graduate_year' => $graduate_year,
            'school' => $school,
            'accountcreated' => true,
        ]);

        ChildInvitee::where('child_id', $student->id)->update(['processed' => '1']);
        // User::addHelpcrunchAccount($student->id);
        \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));
        if ($student->is_child) {
            // User::updateAssociatedUsersMailchimpDetail($student->id);
        }
        $user = User::find($student->id);
        // UpdateMailchimpAudienceJob::dispatch($user);
        if (Auth::attempt(['email' => $student->email, 'password' => request('password')])) {
            return redirect('/#/gameplan')->with('message', "Your account has been successfully created.");
        }
        // $credentials = $request->validate([
        //     'email' => ['required', 'email'],
        //     'password' => ['required'],
        // ]);
        // try{
        //     if (Auth::attempt($credentials)) {
        //         $request->session()->regenerate();
        //         return new UserResource(Auth::user());
        //     }
        // }catch(\Exception $e){
        //     return response()->json([
        //         'messages' => 'The provided credentials do not match our records.','errors'=>[['The provided credentials do not match our records.']]
        //     ], 400);
        // }
    }
}
