<template>
    <div class="border p-7 rounded position-relative bg-white mb-7" :style="cardStyle">
        <div v-if="showFollowButton && student.is_public" class="position-absolute top-0 end-0">
            <button type="button" class="btn d-flex align-items-center gap-1 pt-7 border-0" style="background-color: transparent; color: rgb(164, 164, 164);" @click="$emit('toggle-follow', student.id)" :disabled="followingStates?.[student.id]?.loading">
                <i :class="student.is_following ? 'bi bi-check pe-0 fs-2' : 'bi bi-plus pe-0 fs-2'" style="color: rgb(164, 164, 164);"></i>
                {{ student.is_following ? 'Following' : 'Follow' }}
            </button>
        </div>
        <span v-if="showFollowingBadge && student.following" class="position-absolute top-0 end-0 m-4 text-muted small" style="font-size:15px;">
            <i class="bi bi-check-circle"></i> Following
        </span>
        <div class="d-flex flex-row align-items-center gap-4 mt-5">
            <!-- Profile Image or Initials -->
            <!-- <div v-if="showProfileImage" class="symbol symbol-110px symbol-circle me-4 flex-shrink-0">
                <img :src="`https://picsum.photos/110?random=${student.id}`" alt="Student Profile"
                    class="symbol-label bg-secondary"
                    style="width: 110px; height: 110px; object-fit: cover; border-radius: 50%; background-color: #e0e0e0;" />
            </div> -->
            <div class="symbol symbol-65px symbol-circle">
                <span class="symbol-label fs-1 fw-semibold text-dark bg-secondary"><span v-if="student.is_public">{{ student.initials }}</span></span>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-start mt-8">
                <h3 v-if="student.is_public" class="fw-bold mb-2" :style="showProfileImage ? 'font-size:2rem;' : ''">{{ student.name }}</h3>
                <h3 v-else class="text-muted">Private Student</h3>
                <span v-if="showProfileImage" class="badge bg-light-info text-info mb-2 p-3">Year {{
                    student.profile?.class?.title || 'N/A' }}</span>
                <div v-else class="date rounded">
                    <p class="badge badge-light-info">Year {{ student.year }}</p>
                </div>
            </div>
        </div>
        <div class="mt-8 h-45px overflow-hidden" >
            <span class="fw-semibold" v-if="student.engaged_content?.length">Related Engagement: </span>
            <span v-for="content in student.engaged_content" :key="content" class="badge bg-light-info text-info p-1 mb-1 me-1"><i class="bi bi-check2 text-info me-2"></i> {{ content }}
            </span>
        </div>
        <div class="mt-4">
            <span class="fw-semibold">They're considering:</span>
            <div class="row d-flex justify-content-between mt-2 gap-3 px-3">
                <div class="col-3 text-center flex-fill border border-dashed rounded-3 p-3">
                    <div class="fw-bold fs-3">{{ student.considering?.industries ?? student.industriesCount }}</div>
                    <div class="text-muted">Industries</div>
                </div>
                <div class="col-3 text-center flex-fill border border-dashed rounded-3 p-3">
                    <div class="fw-bold fs-3">{{ student.considering?.jobs ?? student.jobsCount }}</div>
                    <div class="text-muted">Jobs</div>
                </div>
                <div class="col-3 text-center flex-fill border border-dashed rounded-3 p-3">
                    <div class="fw-bold fs-3">{{ student.considering?.companies ?? student.companiesCount }}</div>
                    <div class="text-muted">Companies</div>
                </div>
            </div>
        </div>
        <div class="d-flex mt-8 gap-2 h-45px" :class="{
            'justify-content-between': showViewResponseButton && showViewProfileButton,
            'justify-content-end': !showViewResponseButton && showViewProfileButton,
            'justify-content-start': showViewResponseButton && !showViewProfileButton,
        }">
            <button v-if="showViewResponseButton" class="btn btn-sm btn-light d-flex align-items-center justify-content-center fw-semibold" style="font-size:1.1rem;" :data-bs-toggle="modalToggleAttr" :data-bs-target="modalTarget" @click="$emit('view-response', student)" :disabled="!student.responsePath"><i class="bi bi-eye me-2"></i>View Response</button>
            <a v-if="showViewProfileButton && student.is_public" :href="viewProfileUrl" target="_blank" class="btn btn-light d-flex align-items-center text-black gap-2">
                View Profile
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </a>
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent, PropType } from 'vue';

    export default defineComponent({
        name: 'StudentCardBlock',
        props: {
            student: { type: Object as PropType<any>, required: true },
            showFollowButton: { type: Boolean, default: false },
            showFollowingBadge: { type: Boolean, default: false },
            showProfileImage: { type: Boolean, default: false },
            showViewProfileButton: { type: Boolean, default: true },
            showViewResponseButton: { type: Boolean, default: false },
            followingStates: { type: Object as PropType<Record<number, { loading: boolean }>>, default: undefined },
            cardStyle: { type: Object as PropType<any>, default: undefined },
            modalToggleAttr: { type: String, default: 'modal' },
            modalTarget: { type: String, default: '#kt_modal_viewResponse' },
            viewProfileUrl: { type: String, default: '' },
        },
    });

    
</script>