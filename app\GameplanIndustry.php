<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GameplanIndustry extends Model
{
    protected $fillable = ['gameplan_id', 'industry_category_id', 'priority'];

    protected static function booted()
    {
        // Clear pipeline cache when gameplan industries change
        $clearPipelineCache = function ($gameplanIndustry) {
            app(\App\Services\EmployerPipelineService::class)->clearCacheForGameplanIndustry($gameplanIndustry);
        };

        static::created($clearPipelineCache);
        static::updated($clearPipelineCache);
        static::deleted($clearPipelineCache);
    }

    // Relationships
    public function gameplan()
    {
        return $this->belongsTo(Gameplan::class);
    }

    public function industryCategory()
    {
        return $this->belongsTo(IndustryCategory::class, 'industry_category_id');
    }
}
