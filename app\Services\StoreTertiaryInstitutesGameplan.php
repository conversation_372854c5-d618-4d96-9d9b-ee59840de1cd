<?php

namespace App\Services;

use App\Enums\GameplanQuestionType;
use App\Gameplan;
use App\GameplanQuestion;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Jobs\SyncCustomDataToIntercom;

/**
 * Service to handle creating or updating gameplans for users with tertiary institutes.
 */
class StoreTertiaryInstitutesGameplan
{
    private const STATIC_FIELDS = [
        'industries',
        'jobs',
        'companies',
    ];

    /**
     * Handle the gameplan creation or update process.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handle(Request $request): JsonResponse
    {
        try {
            // Fetch institute details safely
            $school = Auth::user()->school;
            if (!$school || !$school->detail) {
                Log::error('User missing school or detail.', ['user_id' => Auth::id()]);
                return response()->json(['message' => 'Invalid institute data'], 422);
            }

            $instituteId = $school->detail->school_id;
            $instituteType = 'tertiary';

            // Cache institute-specific questions
            $cacheKey = "gameplan_questions_{$instituteType}_{$instituteId}";
            $questions = Cache::remember($cacheKey, now()->addHour(), function () use ($instituteType, $instituteId) {
                return GameplanQuestion::with('options')
                    ->orderBy('sort_order')
                    ->where(function ($query) use ($instituteType, $instituteId) {
                        $query->where('institute_type', $instituteType)
                            ->where(function ($subQuery) use ($instituteId) {
                                $subQuery->whereNull('institute_id')
                                    ->orWhere('institute_id', $instituteId);
                            });
                    })
                    ->orWhereNull('institute_type')
                    ->get();
            });

            // Build validation rules
            $validationRules = $this->buildValidationRules($request, $questions);

            // Validate incoming data
            $validatedData = $request->validate($validationRules);

            // Use a transaction for atomicity
            return DB::transaction(function () use ($validatedData, $school, $questions) {
                // Create or update gameplan
                $gameplan = Gameplan::create(
                    ['user_id' => Auth::id()],
                    []
                );
                // Store static fields
                $this->storeStaticFields($gameplan, $validatedData);

                // Store dynamic field answers
                $this->storeDynamicFields($gameplan, $validatedData, $questions);

                // Clear cache and set cookie
                Cache::forget('lastPlan' . Auth::id());
                Cache::forget('latestGameplan' . Auth::id());
                Cookie::queue('planPopup', true);

                // Dispatch Intercom sync job
                SyncCustomDataToIntercom::dispatchIf(
                    (new Intercom())->isEnabled(),
                    Auth::user(),
                    Auth::user()->intercom_gameplan_data
                );

                return response()->json([
                    'message' => 'Gameplan ' . ($gameplan->wasRecentlyCreated ? 'created' : 'updated') . ' successfully for user with verified school domain',
                    'gameplan' => $gameplan,
                ], $gameplan->wasRecentlyCreated ? 201 : 200);
            });
        } catch (\Exception $e) {
		
            Log::error('Failed to process gameplan.', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->except(['password', 'token']), // Exclude sensitive data
            ]);
            return response()->json(['message' => 'Failed to process gameplan'], 500);
        }
    }

    /**
     * Build validation rules for static and dynamic fields.
     *
     * @param Request $request
     * @param \Illuminate\Database\Eloquent\Collection $questions
     * @return array
     */
    private function buildValidationRules(Request $request, $questions): array
    {
        $rules = [
            'industries' => array_merge(
                ['array'],
                $request->has('industries') ? ['required', 'min:1'] : ['nullable']
            ),
           'jobs' => is_array($request->input('jobs')) && count($request->input('jobs')) > 0
				? ['required', 'array', 'min:1']
				: ['nullable', 'array'],

			'companies' => is_array($request->input('companies')) && count($request->input('companies')) > 0
				? ['required', 'array', 'min:1']
				: ['nullable', 'array'],
        ];

        foreach ($questions as $question) {
            $key = $question->question_key ?: "{$question->type->value}_{$question->id}";
            if (!in_array($key, self::STATIC_FIELDS)) {
                $optionIds = $question->options->pluck('id')->toArray();
                if (empty($optionIds)) {
                    Log::warning('No options found for question.', [
                        'question_id' => $question->id,
                        'key' => $key,
                        'type' => $question->type->value,
                    ]);
                    continue;
                }
                if ($question->type === GameplanQuestionType::CHECKBOX) {
                    $rules[$key] = array_merge(
                        ['array'],
                        $request->has($key) ? ['required', 'min:1'] : ['nullable'],
                        ['*' => 'in:' . implode(',', $optionIds)]
                    );
                } elseif ($question->type === GameplanQuestionType::RADIO) {
                    $rules[$key] = array_merge(
                        ['integer'],
                        $request->has($key) ? ['required'] : ['nullable'],
                        ['in:' . implode(',', $optionIds)]
                    );
                }
            }
        }

        return $rules;
    }

    /**
     * Store static fields (industries, jobs, companies).
     *
     * @param Gameplan $gameplan
     * @param array $validatedData
     * @return void
     */
    private function storeStaticFields(Gameplan $gameplan, array $validatedData): void
    {
        // Industries
        $gameplan->industries()->detach();
        if (!empty($validatedData['industries'])) {
            foreach ($validatedData['industries'] as $priority => $industryId) {
                $gameplan->industries()->attach($industryId, ['priority' => $priority + 1]);
            }
        }

        // Jobs
        $gameplan->jobs()->delete();
        $manualJobs = [];
        $occupationIds = [];
    
        if (!empty($validatedData['jobs'])) {
            foreach ($validatedData['jobs'] as $job) {
                if (is_array($job)) {
                    // Selected from dropdown
                    if (!empty($job['selectedFromDropdown']) && !empty($job['anzsco_occupation_id'])) {
                        $occupationIds[] = $job['anzsco_occupation_id'];
                    }
                } else {
                    // Manually entered
                    $manualJobs[] = ['job_title' => $job];
                }
            }
    
            if (!empty($manualJobs)) {
                $gameplan->jobs()->createMany($manualJobs);
            }
    
            if (!empty($occupationIds)) {
                $gameplan->user->userSelectedOccupations()->sync(array_unique($occupationIds));
            } else {
                $gameplan->user->userSelectedOccupations()->sync([]);
            }
        } else {
            $gameplan->user->userSelectedOccupations()->sync([]);
        }

        // Companies
        $gameplan->companies()->delete();
        if (!empty($validatedData['companies'])) {
            $gameplan->companies()->createMany(
                array_map(fn($company) => ['company_name' => $company], $validatedData['companies'])
            );
        }
    }

    /**
     * Store dynamic field answers in gameplan_question_answer.
     *
     * @param Gameplan $gameplan
     * @param array $validatedData
     * @param \Illuminate\Database\Eloquent\Collection $questions
     * @return void
     */
    private function storeDynamicFields(Gameplan $gameplan, array $validatedData, $questions): void
    {
        $gameplan->questionAnswers()->delete();
        foreach ($questions as $question) {
            $key = $question->question_key ?: "{$question->type->value}_{$question->id}";
            if (!in_array($key, self::STATIC_FIELDS) && !empty($validatedData[$key])) {
                $values = is_array($validatedData[$key]) ? $validatedData[$key] : [$validatedData[$key]];
                foreach ($values as $value) {
                    $option = $question->options->firstWhere('id', $value);
                    if ($option) {
                        $gameplan->questionAnswers()->create([
                            'gameplan_question_id' => $question->id,
                            'gameplan_question_option_id' => $option->id,
                        ]);
                    } else {
                        Log::warning('Invalid option ID for question.', [
                            'question_key' => $key,
                            'value' => $value,
                            'question_id' => $question->id,
                            'type' => $question->type->value,
                        ]);
                    }
                }
            }
        }
    }
}