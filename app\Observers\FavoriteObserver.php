<?php

namespace App\Observers;

use App\Services\EmployerPipelineService;
use Overtrue\LaravelFavorite\Favorite;

class FavoriteObserver
{
    protected $pipelineService;

    public function __construct(EmployerPipelineService $pipelineService)
    {
        $this->pipelineService = $pipelineService;
    }

    /**
     * Handle the favorite "created" event.
     */
    public function created(Favorite $favorite)
    {
        $this->pipelineService->clearCacheForFavorite($favorite);
    }

    /**
     * Handle the favorite "updated" event.
     */
    public function updated(Favorite $favorite)
    {
        $this->pipelineService->clearCacheForFavorite($favorite);
    }

    /**
     * Handle the favorite "deleted" event.
     */
    public function deleted(Favorite $favorite)
    {
        $this->pipelineService->clearCacheForFavorite($favorite);
    }
}
