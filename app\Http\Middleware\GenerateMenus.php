<?php

namespace App\Http\Middleware;

use App\ChildParent;
use App\Menu;
use App\Profile;
use Auth;
use Closure;
use Request;
use Illuminate\Support\Facades\Cache;

class GenerateMenus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if (Auth::check()) {

            if (in_array(Auth::id(), [32091, 433])) {
                \Debugbar::enable();
            }
            \Menu::make('sideNavbar', function ($menu) {
                $menu->add('<div class="title">Home</div>')->after('<div class="icon-thumbnail "><i class="fa fa-home"></i></div>');
                $school = $menu->add('<div class="title">Schools</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');
                $school->add('Index', 'schools')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>')->active('schools/*');
                $school->add('Ordered schools', 'ordered-schools')->after('<div class="icon-thumbnail "><i class="fa fa-list-alt"></i></div>')->active('ordered-schools/*');
                $school->add('School visit requests', 'schoolvisitrequests')->after('<div class="icon-thumbnail "><i class="fa fa-commenting-o"></i></div>');
                $school->add('School visits', 'schoolvisittemplates')->after('<div class="icon-thumbnail "><i class="fa fa-street-view"></i></div>');


                $org = $menu->add('<div class="title">Organisations</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');
                $org->add('Index', 'organisations')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>')->active('organisations/*/edit');
                $org->add('Add new', 'organisations/create')->after('<div class="icon-thumbnail "><i class="fa fa-plus"></i></div>');
                $org->add('Staff', 'staff')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>')->active('staff/*');

                $teacher = $menu->add('<div class="title">Teachers</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-user"></i></div>');
                $teacher->add('Index', 'teachers')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>');
                $teacher->add('Teacher Positions', 'positions')->after('<div class="icon-thumbnail "><i class="fa fa-user-circle-o"></i></div>');
                $teacher->add('Teaching Resources', 'teacherresources')->after('<div class="icon-thumbnail "><i class="fa fa-file-o"></i></div>');
                $teacher->add('Teaching Areas', 'teachingareas')->after('<div class="icon-thumbnail "><i class="fa fa-map-o"></i></div>');
                $teacher->add('Resource Types', 'teacherresourcetypes')->after('<div class="icon-thumbnail "><i class="fa fa-file-text-o"></i></div>');
                $teacher->add('Resource Groups', 'teacherresourcegroups')->after('<div class="icon-thumbnail "><i class="fa fa-object-group"></i></div>');

                $student = $menu->add('<div class="title">Students</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>');
                $student->add('Index', 'students')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>')->active('students/*');
                $student->add('Group Reports', 'reports')->after('<div class="icon-thumbnail "><i class="fa fa-bar-chart"></i></div>');

                $parent = $menu->add('<div class="title">Parent</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-user"></i></div>');
                $parent->add('Index', 'parents')->after('<div class="icon-thumbnail "><i class="fa fa-user"></i></div>');
                $parent->add('Parent Categories', 'parentcategories')->after('<div class="icon-thumbnail "><i class="fa fa-th-large"></i></div>');
                $parent->add('Parent Templates', 'parenttemplates')->after('<div class="icon-thumbnail "><i class="fa fa-window-restore" aria-hidden="true"></i></div>');

                $marker = $menu->add('<div class="title">VWE Marker</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-user"></i></div>');
                $marker->add('Index', 'markers')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>')->active('markers/*');

                $companies = $menu->add('<div class="title">Companies</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-building"></i></div>');
                $companies->add('Index', ['route' => 'companies.index'])->after('<div class="icon-thumbnail "><i class="fa fa-building"></i></div>')->active('companies/*');
                $companies->add('Employers', ['route' => 'employers.index'])->after('<div class="icon-thumbnail "><i class="fa fa-user-circle-o"></i></div>')->active('employers/*');

                // $staff = $menu->add('<div class="title">Staff</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>');
                // $staff->add('Index', 'staff')->after('<div class="icon-thumbnail "><i class="fa fa-users"></i></div>')->active('staff/*');

                $profiler = $menu->add('<div class="title">Profiler</div><div class="arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-vcard-o"></i></div>');
                $profiler->add('Units', ['route' => 'units.index'])->after('<div class="icon-thumbnail "><i class="fa fa-window-maximize" aria-hidden="true"></i></div>')->active('profiler/units/*');
                $profiler->add('Groups', 'groups')->after('<div class="icon-thumbnail "><i class="fa fa-object-group" aria-hidden="true"></i></div>');
                $profiler->add('<div class="title">Job Categories</div>', 'jobcategories')->after('<div class="icon-thumbnail "><i class="fa fa-th-large"></i></div>');
                $profiler->add('<div class="title">Nominees</div>', 'nominees')->after('<div class="icon-thumbnail "><i class="fa fa-user-circle-o"></i></div>');


                // new end
                $profiling_posts = $menu->add('<div class="title">Profiling Posts</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-inbox"></i></div>');

                $profiling_posts->add('Index', 'profiling-posts')->after('<div class="icon-thumbnail "><i class="fa fa-book"></i></div>');

                $ANZSCO_data = $menu->add('<div class="title">ANZSCO Data</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-inbox"></i></div>');

                $ANZSCO_data->add('Index', 'profiling-posts/anzsco-data/occupations')->after('<div class="icon-thumbnail "><i class="fa fa-book"></i></div>');

                $ANZSCO_data->add('AllSpecialistCluster', 'SpecialistCluster')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');

                $ANZSCO_data->add('AllSpecialistClusterFamilies', 'specialistclusterfamilies')->after('<div class="icon-thumbnail"><i class="fa fa-building-o"></i></div>');

                $ANZSCO_data->add('AllSpecialistTasks', 'specialist-tasks')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');

                $ANZSCO_data->add('AllTechnologyTool', 'TechnologyTool')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');

                $ANZSCO_data->add('AllTechnologyToolCategory', 'TechnologyToolCategory')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');

                $ANZSCO_data->add('AllCorecompetencyAnchorValues', 'core-competency-anchor-values')->after('<div class="icon-thumbnail "><i class="fa fa fa-address-book"></i></div>');

                $industries = $menu->add('<div class="title">Industries</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-industry"></i></div>')->active('industries/*')->active('industrytemplates/*');
                $industries->add('<div class="title">Index</div>', 'industrycategories')->after('<div class="icon-thumbnail "><i class="fa fa-industry"></i></div>')->active('industrycategories/*');
                $industries->add('<div class="title">Templates</div>', 'industries/templates')->after('<div class="icon-thumbnail "><i class="fa fa-window-maximize"></i></div>');
                $industries->add('<span class="title">Student Enquiries</span>', 'industryunitStudentEnquiries')->after('<span class="icon-thumbnail "><i class="fa fa-question-circle"></i></span>');
                $industries->add('<span class="title">Parent Enquiries</span>', 'industryunitParentEnquiries')->after('<span class="icon-thumbnail "><i class="fa fa-question-circle"></i></span>');
                $industries->add('<span class="title">Send to Parent</span>', 'industryunitParentEmails')->after('<span class="icon-thumbnail "><i class="fa fa-envelope-o"></i></span>');
                $industries->add('<div class="title">Nominees</div>', 'interview/nominees')->after('<div class="icon-thumbnail "><i class="fa fa-user-circle-o"></i></div>');

                $tasks = $menu->add('<div class="title">Tasks</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-bars"></i></div>');
                $tasks->add('Lessons', ['route' => 'lessons.index'])->after('<div class="icon-thumbnail "><i class="fa fa-tasks"></i></div>')->active('lessons/*');
                $tasks->add('Work, Health & Safety', ['route' => 'workhealthsafetytemplates.index'])->after('<div class="icon-thumbnail "><i class="fa fa-window-restore"></i></div>')->active('workhealthsafetytemplates/*');
                $tasks->add('Virtual work experience', 'workexperiencetemplates')->after('<div class="icon-thumbnail "><i class="fa fa-window-restore"></i></div>')->active('workexperiencetemplates/*');
                $tasks->add('Skills Training', ['route' => 'skillstrainingtemplates.index'])->after('<div class="icon-thumbnail "><i class="fa fa-window-restore"></i></div>')->active('skillstrainingtemplates/*');
                $tasks->add('Resume’s & Interview', ['route' => 'resumesinterviewstemplates.index'])->after('<div class="icon-thumbnail "><i class="fa fa-window-restore"></i></div>')->active('resumesinterviewstemplates/*');
                $tasks->add('Work Ready', ['route' => 'workreadytemplates.index'])->after('<div class="icon-thumbnail "><i class="fa fa-window-restore"></i></div>')->active('workreadytemplates/*');
                $tasks->add('Lessons Categories/Study Area', ['route' => 'lessoncategories.index'])->after('<div class="icon-thumbnail "><i class="fa fa-th-large"></i></div>');
                $tasks->add('Experience Categories', ['route' => 'experiencecategories.index'])->after('<div class="icon-thumbnail "><i class="fa fa-th-large"></i></div>');
                $tasks->add('Skills Training Categories', ['route' => 'skillstrainingcategories.index'])->after('<div class="icon-thumbnail "><i class="fa fa-th-large"></i></div>');
                $tasks->add('Responses', ['route' => 'wew.responses'])->after('<div class="icon-thumbnail "><i class="fa fa-comments-o"></i></div>')->active('wew/responses/*');
                $tasks->add('Badges', ['route' => 'badges.index'])->after('<div class="icon-thumbnail "><i class="fa fa-certificate"></i></div>')->active('badges/*');

                // $lesson = $menu->add('<div class="title">Online lesson</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-tasks"></i></div>');

                // $wew->add('Responses', 'lessons/responses')->after('<div class="icon-thumbnail "><i class="fa fa-comments-o"></i></div>');

                $quiz = $menu->add('<div class="title">Quiz</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-lightbulb-o"></i></div>');
                $quiz->add('Quizzes', ['route' => 'quizzes.index'])->after('<div class="icon-thumbnail "><i class="fa fa-lightbulb-o"></i></div>')->active('quizzes/*');
                $quiz->add('Questions', ['route' => 'questions.index'])->after('<div class="icon-thumbnail "><i class="fa fa-question-circle-o"></i></div>')->active('questions/*');

                // $work = $menu->add('<div class="title">Work experience</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-th-large"></i></div>');
                // $work->add('Templates', 'workexperiencetemplates')->after('<div class="icon-thumbnail "><i class="fa fa-window-restore"></i></div>');
                // $work->add('Responses', ['route' => 'student.responses'])->after('<div class="icon-thumbnail "><i class="fa fa-comments-o"></i></div>');

                $course = $menu->add('<div class="title">Courses</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa pg-contact_book"></i></div>')->active('courses/*');
                $course->add('<div class="title">Index</div>', ['route' => 'courses.index'])->after('<div class="icon-thumbnail "><i class="fa pg-contact_book"></i></div>');
                $course->add('<div class="title">Import</div>', ['route' => 'courses.import'])->after('<div class="icon-thumbnail "><i class="fa fa-upload"></i></div>');
                // $course->add('<div class="title">Course Categories</div>', 'coursecategories')->after('<div class="icon-thumbnail "><i class="fa fa-columns "></i></div>');

                $uni = $menu->add('<div class="title">Universities/TAFE</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-university"></i></div>');
                $uni->add('Universities', 'universities')->after('<div class="icon-thumbnail "><i class="fa fa-university"></i></div>');
                $uni->add('TAFE', 'institutes')->after('<div class="icon-thumbnail "><i class="fa fa-building"></i></div>');
                $uni->add('Colleges', 'colleges')->after('<div class="icon-thumbnail "><i class="fa fa-building-o"></i></div>');
                $uni->add('Import', 'instituteimport')->after('<div class="icon-thumbnail "><i class="fa fa-upload"></i></div>');

                $calendar = $menu->add('<div class="title">Calendar</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-calendar"></i></div>');
                $calendar->add('Events', 'events')->after('<div class="icon-thumbnail "><i class="fa fa-calendar-o"></i></div>');
                $calendar->add('Event Categories', 'eventcategories')->after('<div class="icon-thumbnail "><i class="fa fa-th-large" aria-hidden="true"></i></div>');
                $calendar->add('Import Events', ['route' => 'events.import'])->after('<div class="icon-thumbnail "><i class="fa fa-upload" aria-hidden="true"></i></div>');


                $subject = $menu->add('<div class="title">SSQ</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-book"></i></div>');
                $subject->add('Subjects', 'subjects')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Subject Areas', 'subject-areas')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Subject Types', 'subject-types')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Course Types', 'subject-course-types')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Learning Styles', 'learning-styles')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Study Habits', 'ssq-study-habits')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Skills', 'ssq-skills')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Interests', 'ssq-interests')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Languages', 'ssq-languages')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');
                $subject->add('Jobs', 'ssq-jobs')->after('<div class="icon-thumbnail "><i class="fa fa-list"></i></div>');

                $ttk = $menu->add('<div class="title">Things to know</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-lightbulb-o"></i></div>');
                // $ttk->add('Templates', ['route' => 'thingstoknow.templates.index'])->after('<div class="icon-thumbnail "><i class="fa fa-columns" aria-hidden="true"></i></div>')->active('thingstoknow/templates/*');
                $ttk->add('Index', ['route' => 'thingstoknow.categories.index'])->after('<div class="icon-thumbnail "><i class="fa fa-th-large" aria-hidden="true"></i></div>')->active('thingstoknow/categories/*');
                $ttk->add('Templates', ['route' => 'thingstoknowtemplates.all'])->after('<div class="icon-thumbnail "><i class="fa fa-columns" aria-hidden="true"></i></div>')->active('thingstoknow/alltemplates/*');

                $cv = $menu->add('<div class="title">CV</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-file-text-o"></i></div>');
                $cv->add('CV Types', ['route' => 'cvtypes.index'])->after('<div class="icon-thumbnail "><i class="fa fa-bars" aria-hidden="true"></i></div>');
                $cv->add('CV Sections', ['route' => 'cvsections.index'])->after('<div class="icon-thumbnail "><i class="fa fa-window-restore" aria-hidden="true"></i></div>');
                $cv->add('CV Templates', ['route' => 'cvtemplates.index'])->after('<div class="icon-thumbnail "><i class="fa fa-file-pdf-o" aria-hidden="true"></i></div>');
                $cv->add('CV Tips', ['route' => 'cvtips.index'])->after('<div class="icon-thumbnail "><i class="fa fa-list" aria-hidden="true"></i></div>');

                $notices = $menu->add('<div class="title">Notices</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-bell-o"></i></div>');
                $notices->add('Index', 'notices')->after('<div class="icon-thumbnail "><i class="fa fa-sticky-note-o"></i></div>')->active('notices/*');
                $notices->add('Teachers Notices', ['route' => 'teacher.notices'])->after('<div class="icon-thumbnail "><i class="fa fa-sticky-note-o" aria-hidden="true"></i></div>');
                $notices->add('Staff Notices', ['route' => 'staff.notices'])->after('<div class="icon-thumbnail "><i class="fa fa-sticky-note-o" aria-hidden="true"></i></div>');


                $scholar = $menu->add('<div class="title">Sholarships</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-graduation-cap"></i></div>');
                $scholar->add('Index', ['route' => 'scholarships.index'])->after('<div class="icon-thumbnail "><i class="fa fa-graduation-cap" aria-hidden="true"></i></div>')->active('scholarships/*');
                $scholar->add('Application tips', ['route' => 'scholarshiptips.index'])->after('<div class="icon-thumbnail "><i class="fa fa-list" aria-hidden="true"></i></div>');
                $scholar->add('Help requests', 'scholarshipshelprequests')->after('<div class="icon-thumbnail "><i class="fa fa-question-circle" aria-hidden="true"></i></div>');
                $scholar->add('Providers', 'scholarshipproviders')->after('<div class="icon-thumbnail "><i class="fa fa-university" aria-hidden="true"></i></div>');
                $scholar->add('Nominees', ['route' => 'scholarship.nominees'])->after('<div class="icon-thumbnail "><i class="fa fa-user-circle-o" aria-hidden="true"></i></div>');
                $scholar->add('Target Groups', 'targetgroups')->after('<div class="icon-thumbnail "><i class="fa fa-bullseye" aria-hidden="true"></i></div>');
                $scholar->add('Field of Study', 'fieldofstudies')->after('<div class="icon-thumbnail "><i class="fa fa-braille" aria-hidden="true"></i></div>');

                // $mag = $menu->add('<div class="title">E-magazine</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-book"></i></div>');
                // $mag->add('Index', 'magazines')->after('<div class="icon-thumbnail "><i class="fa fa-book"></i></div>');
                // $mag->add('Magazine Editions', 'editions')->after('<div class="icon-thumbnail "><i class="fa fa-object-ungroup"></i></div>');

                $gameplan = $menu->add('<div class="title">Gameplan</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-gamepad"></i></div>');
                $gameplan->add('Questions', 'gameplanQuestions')->after('<div class="icon-thumbnail "><i class="fa fa-gamepad"></i></div>');

                $extra = $menu->add('<div class="title">Extra</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-inbox"></i></div>');
                $extra->add('<div class="title">Media</div>', 'medias')->after('<div class="icon-thumbnail "><i class="fa fa-file-image-o"></i></div>');
                $extra->add('<div class="title">Videos</div>', 'cloudvideos')->after('<div class="icon-thumbnail "><i class="fa fa-file-video-o"></i></div>');
                $extra->add('Galleries', 'galleries')->after('<div class="icon-thumbnail "><i class="fa fa-picture-o"></i></div>')->active('galleries/*');
                $extra->add('Banners', 'banners')->after('<div class="icon-thumbnail "><i class="fa fa-window-maximize"></i></div>')->active('banners/*');
                // $extra->add('<div class="title">Revolution Sliders</div>', 'revslider')->after('<div class="icon-thumbnail "><i class="fa fa-sliders"></i></div>');
                // $extra->add('<div class="title">Blog</div>', 'blog')->after('<div class="icon-thumbnail "><i class="fa fa-rss"></i></div>');
                $extra->add('<div class="title">Email templates</div>', 'emails')->after('<div class="icon-thumbnail "><i class="fa fa-envelope-o"></i></div>');
                $extra->add('<div class="title">Seo pages</div>', 'seopages')->after('<div class="icon-thumbnail "><i class="fa fa-search "></i></div>');
                // $extra->add('<div class="title">Articles</div>', 'articles')->after('<div class="icon-thumbnail "><i class="fa fa-newspaper-o"></i></div>');
                // $extra->add('<div class="title">Webinars</div>', 'webinar')->after('<div class="icon-thumbnail "><i class="fa fa-newspaper-o"></i></div>');

                // $marker = $menu->add('<div class="title">Dashboard</div><div class=" arrow"></div>', 'javascript:;')->after('<div class="icon-thumbnail "><i class="fa fa-dashboard"></i></div>');
            });

            \Menu::make('primaryTeacherSideNavbar', function ($menu) {
                $menu->add('<div class="title arrow-on-active">Dashboard</div>', 'home');
                $menu->add('<div class="title bold">Account</div>', 'javascript:;');
                $menu->add('<div class="level-one-menu">Your profile</div>', ['route' => 'profile-edit']);
                if (Auth::user()->isTeacher() && Auth::user()->hasFullAccess()) {
                    $menu->add('<div class="level-one-menu">Teachers</div>', 'teachers');
                }

                $menu->add('<div class="title bold">Resources & Tasks</div>', 'javascript:;');
                $menu->add('<div class="level-one-menu">Activities and Lessons</div>', ['route' => 'activitiesLessons'])->active('wew/*');
            });

            \Menu::make('teacherSideNavbar', function ($menu) {
                $menu->add('<div class="title arrow-on-active">Dashboard</div>', 'home');
                $menu->add('<div class="title bold">Account</div>', 'javascript:;');
                $menu->add('<div class="level-one-menu">Your profile</div>', ['route' => 'profile-edit']);
                if (Auth::user()->hasManagerAccess()) {
                    if (Request::segment(1) != 'tasks' && (is_numeric(Request::segment(2)) || is_numeric(Request::segment(3)))) {
                        $menu->add('<div class="level-one-menu">Students</div>', 'students')->active(Request::segment(1) . '/*/' . Request::segment(3))->active(Request::segment(1) . '/' . Request::segment(2) . '/*')/*->active('students/*')*/;
                    } else {
                        $menu->add('<div class="level-one-menu">Students</div>', 'students');
                    }
                    if (Auth::user()->hasFullAccess()) {
                        if (Auth::user()->isStaff()) {
                            $menu->add('<div class="level-one-menu">Staff</div>', 'staff');
                        } else {
                            $menu->add('<div class="level-one-menu">Teachers</div>', 'teachers');
                        }
                    }
                    $menu->add('<div class="level-one-menu">Reports</div>', 'reports');
                }


                $menu->add('<div class="title bold">Resources & Tasks</div>', 'javascript:;');
                $menu->add('<div class="level-one-menu">Noticeboard</div>', 'noticeboard');
                $menu->add('<div class="level-one-menu">Teaching resources</div>', 'teacherresources');
                if (Auth::user()->hasManagerAccess()) {
                    $studenttasks = $menu->add('<div class="level-one-menu have-child">Student Tasks</div>', 'javascript:;');
                    if (Auth::user()->hasFullAccess()) {
                        $studenttasks->add('<div class="level-two-menu">Manage</div>', 'tasks-manage');
                    }
                    $studenttasks->add('<div class="level-two-menu">Responses</div>', ['route' => 'tasks.student'])->active('tasks/*');
                }

                if (Auth::user()->hasManagerAccess()) {
                    if (Auth::user()->hasAccessToVirtualworkexperience()) {
                        // $vwe = $menu->add('<div class="level-one-menu have-child">Virtual Work Experience</div>', 'javascript:;');
                        // $vwe->add('<div class="level-two-menu">Responses</div>', ['route' => 'tasks.workexperience']);
                        $vwe = $menu->add('<div class="level-one-menu have-child">Work Experience</div>', 'javascript:;');
                        if (Auth::user()->hasFullAccess()) {
                            $vwe->add('<div class="level-two-menu">Manage</div>', ['route' => 'wew.manage']);
                        }
                        $vwe->add('<div class="level-two-menu">Responses</div>', ['route' => 'wew.responses'])->active('wew/responses/*');
                    }
                }
            });

            \Menu::make('studentSideNavbar', function ($menu) {
                $menu->add('<div class="title">Home</div>', 'home');

                $you = $menu->add('<div class="title">You</div>', 'javascript:;');

                // $you->add('Your account', ['route' => 'profile-edit']);

                if (Request::segment(1) == "profiles" && Request::segment(2) == "edit") {
                    $you->add('Your account', ['route' => 'profile-edit']);
                } elseif (Request::segment(1) == "home") {
                    $you->add('Your account', ['route' => 'profile-edit']);
                } elseif (Request::segment(1) == "gameplan") {
                    $you->add('Your account', 'gameplan');
                } elseif (Request::segment(1) == "studentContent") {
                    $you->add('Your account', 'studentContent');
                } elseif (Request::segment(1) == "studentCourses") {
                    $you->add('Your account', 'studentCourses');
                } elseif (Request::segment(1) == "studentScholarships") {
                    $you->add('Your account', 'studentScholarships');
                } elseif (Request::segment(1) == "profiler" && Request::segment(2) == "result") {
                    $you->add('Your account', 'profiler/result');
                } elseif (Request::segment(1) == "profiler") {
                    $you->add('Your account', 'profiler');
                } elseif (Request::segment(1) == "completed-tasks") {
                    $you->add('Your account', 'completed-tasks');
                } elseif (Request::segment(1) == "completed-Workexperience") {
                    $you->add('Your account', 'completed-Workexperience');
                } elseif (Request::segment(1) == "students" || Request::segment(2) == Auth::id() || Request::segment(3) == "notes") {
                    $you->add('Your account', 'students/' . Auth::id() . '/notes');
                } elseif (Request::segment(1) == "studentcv") {
                    $you->add('Your account', 'studentcv');
                } elseif (Request::segment(1) == "timeline") {
                    $you->add('Your account', 'timeline');
                } else {
                    $you->add('Your Account', ['route' => 'profile-edit']);
                }

                // $you->add('Your profiling', 'profiler')->active('profiler/*');
                // $you->add('Your game plan', 'gameplan');
                // $you->add('Your timeline', 'timeline');

                // Optimization
                // $you->add('Log out', ['url' => 'logout', 'onclick' => "event.preventDefault(); document.getElementById('logout-form').submit();"]);

                $explore = $menu->add('<div class="title">Explore</div>', 'javascript:;');
                $explore->add('Noticeboard', 'noticeboard')->active('noticeboard/*');
                $explore->add('Industries', 'exploreindustries')->active('exploreindustries/*');
                // $explore->add('Things to know', 'explorethingstoknow')->active('explorethingstoknow/*');

                $explore->add('Tasks', ['route' => 'tasks.index'])->active('tasks/*');
                // if (Auth::user()->hasAccessToVirtualworkexperience()) {
                //     $explore->add('Virtual Work Experience', 'exploreworkexperience')->active('exploreworkexperience/*');
                // }
                $explore->add('E-magazine', 'e-magazines/editions')->active('e-magazines/*');
                $explore->add('Career-Profiling', 'career/profiling')->active('career-profiling/*');
                // $explore->add('Video-Profiling', 'video/profiling')->active('video-profiling/*');
                // $explore->add('Calendar', 'events');
                $explore->add('Scholarship finder', 'scholarships')->active('scholarships/*');
                $explore->add('Job finder', 'jobfinder')->active('jobfinder/*');
                $explore->add('Resume builder', 'cvs')->active('cvs/*');
                $explore->add('Course finder', 'coursefinder')->active('coursefinder/*');
                $explore->add('ePortfolio', 'eportfolio')->active('eportfolio/*');
                // if (Auth::user()->belongs_to_school) {
                //     $explore->add('Subject selections <i class="fa fa-question-circle" ></i>', 'javascript:;')->attr(['data-toggle' => 'modal', 'data-target' => '#modalSubjectSelection']);
                // }

                if (Auth::check()) {
                    $profile = Cache::remember('profile' . Auth::id(), now()->addDays(3), function () {
                        return Auth::user()->profile;
                    });
                    if ($profile) {
                        // $wewmenus = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->where('school_id', [Auth::user()->school_id, Auth::user()->organisation_id])->pluck('title')->intersect()->toArray();
                        if (Auth::user()->school_id && Auth::user()->organisation_id) {

                            $scmenus = Cache::remember('wewmenuY' . $profile->standard_id . 'S' . Auth::user()->school_id, now()->addDays(3), function () use ($profile) {
                                return Menu::whereYearId($profile->standard_id)->whereParentMenu('Work Experience Week')
                                    ->when(Auth::user()->campuses()->exists(), function ($q) {
                                        return $q->whereCampusId(Auth::user()->campuses->first()->id);
                                    })
                                    ->when(Auth::user()->campuses()->doesntExist(), function ($q) {
                                        return $q->whereSchoolId(Auth::user()->school_id);
                                    })->pluck('title');
                            });

                            $orgmenus = Cache::remember('wewmenuY' . $profile->standard_id . 'S' . Auth::user()->organisation_id,  now()->addDays(3),function () use ($profile) {
                                return  Menu::whereYearId($profile->standard_id)->whereParentMenu('Work Experience Week')
                                    ->when(Auth::user()->orgCampuses()->exists(), function ($q) {
                                        return $q->whereCampusId(Auth::user()->orgCampuses->first()->id);
                                    })
                                    ->when(Auth::user()->orgCampuses()->doesntExist(), function ($q) {
                                        return $q->whereSchoolId(Auth::user()->organisation_id);
                                    })->pluck('title');
                            });

                            if (($orgmenus->contains('Work Experience Week') && !$scmenus->contains('Work Experience Week')) || (!$orgmenus->contains('Work Experience Week') && $scmenus->contains('Work Experience Week'))) {
                                if ($orgmenus->contains('Work Experience Week')) {
                                    $orgmenus = collect('Work Experience Week');
                                }
                                if ($scmenus->contains('Work Experience Week')) {
                                    $scmenus = collect('Work Experience Week');
                                }
                                $wewmenus = $orgmenus->concat($scmenus)->unique()->toArray();
                                $key = array_search('Work Experience Week', $wewmenus);
                                unset($wewmenus[$key]);
                            } else {
                                $wewmenus = $orgmenus->intersect($scmenus)->toArray();
                            }
                        } elseif (Auth::user()->organisation_id && !Auth::user()->school_id) {
                            $wewmenus = Cache::remember('wewmenuY' . $profile->standard_id . 'S' . Auth::user()->organisation_id,  now()->addDays(3), function () use ($profile) {
                                return  Menu::whereYearId($profile->standard_id)->whereParentMenu('Work Experience Week')
                                    ->when(Auth::user()->orgCampuses()->exists(), function ($q) {
                                        return $q->whereCampusId(Auth::user()->orgCampuses->first()->id);
                                    })
                                    ->when(Auth::user()->orgCampuses()->doesntExist(), function ($q) {
                                        return $q->whereSchoolId(Auth::user()->organisation_id);
                                    })->pluck('title');
                            })->toArray();
                        } elseif (!Auth::user()->organisation_id && Auth::user()->school_id) {
                            $wewmenus = Cache::remember('wewmenuY' . $profile->standard_id . 'S' . Auth::user()->school_id, now()->addDays(3), function () use ($profile) {
                                return Menu::whereYearId($profile->standard_id)->whereParentMenu('Work Experience Week')
                                    ->when(Auth::user()->campuses()->exists(), function ($q) {
                                        return $q->whereCampusId(Auth::user()->campuses->first()->id);
                                    })
                                    ->when(Auth::user()->campuses()->doesntExist(), function ($q) {
                                        return $q->whereSchoolId(Auth::user()->school_id);
                                    })->pluck('title');
                            })->toArray();
                        }
                    }
                }

                if (Auth::user()->is_child || Auth::user()->role->name == "Individual Student") {
                    $wew = $menu->add('<div class="title">Work Experience</div>', 'javascript:;');
                    // $wew->add('Overview', ['route' => 'wew.overview']);
                    $wew->add('Work, Health & Safety', ['route' => 'wew.workhealthsafety.index'])->active('wew/workhealthsafety/*');
                    $wew->add('Virtual Work Experience', 'exploreworkexperience')->active('exploreworkexperience/*');
                    $wew->add('Skills Training', ['route' => 'wew.skillstraining.index'])->active('wew/skillstraining/*');
                    $wew->add('Resumes & Interviews', ['route' => 'wew.resumesinterviews.index'])->active('wew/resumesinterviews/*');
                    $wew->add('Work Ready', ['route' => 'wew.workready.index'])->active('wew/workready/*');
                    // $wew->add('<span data-toggle="tooltip" title="Launching May 21">Work Ready <i class="fa fa-lock"></i><span>', 'javascript:;');
                    // $wew->add('The Future of Work', ['route' => 'wew.futureofwork']);
                } elseif (!Auth::user()->isParent() && Auth::user()->hasAccessToVirtualworkexperience() && (!in_array("Work Experience Week", $wewmenus))) {
                    if (!in_array("Work Experience Week", $wewmenus)) {
                        $wew = $menu->add('<div class="title">Work Experience</div>', 'javascript:;');
                    }
                    // if (!
                    // in_array("Work Experience Week", $wewmenus)) {
                    //     $wew->add('Overview', ['route' => 'wew.overview']);
                    // }
                    if (!in_array("Work Health & Safety", $wewmenus)) {
                        $wew->add('Work, Health & Safety', ['route' => 'wew.workhealthsafety.index'])->active('wew/workhealthsafety/*');
                    }
                    if (!in_array("Virtual Work Experience", $wewmenus)) {
                        $wew->add('Virtual Work Experience', 'exploreworkexperience')->active('exploreworkexperience/*');
                    }
                    if (!in_array("Skills Training", $wewmenus)) {
                        $wew->add('Skills Training', ['route' => 'wew.skillstraining.index'])->active('wew/skillstraining/*');
                    }
                    if (!in_array("Resumes + Interviews", $wewmenus)) {
                        $wew->add('Resumes & Interviews', ['route' => 'wew.resumesinterviews.index'])->active('wew/resumesinterviews/*');
                    }
                    if (!in_array("Work Ready", $wewmenus)) {
                        $wew->add('Work Ready', ['route' => 'wew.workready.index'])->active('wew/workready/*');
                    }
                    // if (!in_array("Future for work", $wewmenus)) {

                    //     $wew->add('The Future of Work', ['route' => 'wew.futureofwork']);
                    // }
                    // $wew->add('<span data-toggle="tooltip" title="Launching May 21">Work Ready <i class="fa fa-lock"></i><span>', 'javascript:;');
                }
            });

            \Menu::make('parentSideNavbar', function ($menu) {
                $menu->add('<div class="title">Home</div>', 'home');

                $you = $menu->add('<div class="title">You</div>', 'javascript:;');
                $you->add('Your Account', ['route' => 'profile-edit']);
                $you->add('Logout', ['url' => 'logout', 'onclick' => "event.preventDefault(); document.getElementById('logout-form').submit();"]);

                if (Auth::user()->isParent()) {
                    // $children = ChildParent::find(Auth::id());
                    // if ($children) {

                    // $children = $children->children()->whereHas('profile', function ($q) {
                    //     $q->where('accountcreated', 1);
                    // })->with('profile')->get();
                    if (Auth::user()->childInvitees()->exists()) {
                        $urchildren = $menu->add('<div class="title">Your Children</div>', 'javascript:;');
                        foreach (Auth::user()->childInvitees as $invitee) {
                            // if ($child->has_completed_profiler) {
                            //     $attributes = [
                            //         'route' => ['children.profiling', $child->id],
                            //         'class' => 'm-l-15'
                            //     ];
                            // } else {
                            //     $attributes = [
                            //         'url' => 'javascript:;',
                            //         'class' => 'm-l-15 disabled',
                            //         'data-toggle' => "tooltip",
                            //         'title' => "Incomplete"
                            //     ];
                            // }

                            // if (Request::segment(1) == "profiles" && Request::segment(2) == "edit"  && Request::segment(3) == $child->id) {
                            //     $urchildren->add($child->name, 'profiles/edit/'.$child->id);
                            // } elseif (Request::segment(1) == "home") {
                            //     $urchildren->add($child->name, ['route' => 'profile-edit', $child->id]);
                            // } elseif (Request::segment(1) == "gameplan" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'gameplan/'.$child->id);
                            // } elseif (Request::segment(1) == "studentContent" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'studentContent/'.$child->id);
                            // } elseif (Request::segment(1) == "studentCourses" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'studentCourses/'.$child->id);
                            // } elseif (Request::segment(1) == "studentScholarships" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'studentScholarships/'.$child->id);
                            // } elseif (Request::segment(1) == "profiler" && Request::segment(2) == "result" && Request::segment(3) == $child->id) {
                            //     $urchildren->add($child->name, 'profiler/result/'.$child->id);
                            // } elseif (Request::segment(1) == "profiler" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'profiler/'.$child->id);
                            // } elseif (Request::segment(1) == "completed-tasks" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'completed-tasks/'.$child->id);
                            // } elseif (Request::segment(1) == "completed-Workexperience" && Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'completed-Workexperience/'.$child->id);
                            // } elseif (Request::segment(1) == "studentcv" &&  Request::segment(2) == $child->id) {
                            //     $urchildren->add($child->name, 'studentcv/'.$child->id);
                            // } elseif (Request::segment(1) == "children" || Request::segment(2) == $child->id || Request::segment(3) == "timeline" ) {
                            //     $urchildren->add($child->name, 'children/' .$child->id. '/timeline');
                            // } elseif (Request::segment(1) == "students" || Request::segment(2) == $child->id || Request::segment(3) == "notes") {
                            //     $urchildren->add($child->name, 'students/' . $child->id . '/notes');
                            // }   else {
                            //     $urchildren->add($child->name, 'profiles/edit/'.$child->id);
                            // }
                            if ($invitee->processed) {
                                $urchildren->add($invitee->child->name, 'profiles/edit/' . $invitee->child_id)->active(Request::segment(1) . '/*/' . $invitee->child_id)->active(Request::segment(1) . '/' . $invitee->child_id . '/*');
                            } else {
                                if($invitee->child){
                                    if ($invitee->child->profile && $invitee->child->profile->accountcreated) {
                                        $title = 'Pending account connection. ' . $invitee->child->name . ' has an account but has not connected it to yours yet. You can reinvite them to do this from your profile.';
                                    } else {
                                        $title = 'Pending account creation. ' . $invitee->child->name . ' has received an invitation to create their account and connect it to yours, but has not set this up yet. You can reinvite them to do this from your profile.';
                                    }
                                    $urchildren->add($invitee->child->name, 'javascript:;')
                                        ->attr([
                                            'class' => 'disabled',
                                            'data-toggle' => "tooltip",
                                            'title' => $title
                                        ]);
                                }
                            }
                            // $urchildren->add('Profiling Result', $attributes);
                            // $urchildren->add('Game plan', ['route' => ['children.gameplan', $child->id], 'class' => 'm-l-15']);
                            // $urchildren->add('Timeline', ['route' => ['children.timeline', $child->id], 'class' => 'm-l-15']);
                        }
                    }
                    // }
                }

                $explore = $menu->add('<div class="title">Explore</div>', 'javascript:;');
                $explore->add('Noticeboard', 'noticeboard')->active('noticeboard/*');
                $explore->add('Tasks', ['route' => 'tasks.index'])->active('tasks/*');
                $explore->add('Industries', 'exploreindustries')->active('exploreindustries/*');
                // $explore->add('Things to know', 'thingstoknow')->active('thingstoknow/*');
                if (Auth::user()->hasAccessToVirtualworkexperience()) {
                    $explore->add('Work experience', 'exploreworkexperience')->active('exploreworkexperience/*');
                }
                $explore->add('E-magazine', 'e-magazines/editions')->active('e-magazines/*');
                $explore->add('Career-Profiling', 'career/profiling')->active('career-profiling/*');
                // $explore->add('Video-Profiling', 'video/profiling')->active('video-profiling/*');
                // $explore->add('Calendar', 'events');
                $explore->add('Scholarship finder', 'scholarships')->active('scholarships/*');
                $explore->add('Job finder', 'jobfinder');
                $explore->add('Resume builder', 'cvs')->active('cvs/*');
                // $explore->add('Live Sessions', 'sessions');
            });
            \Menu::make('markerSideNavbar', function ($menu) {
                $menu->add('<div class="title">Home</div>', 'home');

                $you = $menu->add('<div class="title">You</div>', 'javascript:;');
                $you->add('Edit profile', ['route' => 'profile-edit']);
                $you->add('Logout', ['url' => 'logout', 'onclick' => "event.preventDefault(); document.getElementById('logout-form').submit();"]);

                $explore = $menu->add('<div class="title">Explore</div>', 'javascript:;');
                $explore->add('Responses', ['route' => 'student.responses'])/*->active('tasks/*')*/;
            });
        }

        return $next($request);
    }
}

