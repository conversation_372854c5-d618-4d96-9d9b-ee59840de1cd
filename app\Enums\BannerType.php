<?php

namespace App\Enums;

enum BannerType: string
{
    case Lessons = 'Lessons';
    case VirtualWorkExperience = 'Virtual Work Experience';
    case SkillsTraining = 'Skills Training';
    case Industries = 'Industries';
    case CourseFinder = 'Course Finder';
    case JobFinder = 'Job Finder';
    case ScholarshipFinder = 'Scholarship Finder';
    case EmployerPipeline = 'Employer Pipeline';
    case EmployerCourse = 'Employer Course';



    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}